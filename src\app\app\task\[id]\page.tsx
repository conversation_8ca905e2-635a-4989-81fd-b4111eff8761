'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import TaskBottomTabs from './task-components/bottom-tab'
import DeadlineAlert from './task-components/deadline-alert'
import DependentTaskAccordian from './task-components/dependent-task-accordian'
import TaskDetailsCard from './task-components/task-details-card'
import { useMutation, useQuery } from '@tanstack/react-query'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { Bug, Loader, OctagonAlert } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import MarkdownRenderer from '@/components/markdown-renderer'
import { getDateDifference } from '@/utils/get-date-difference.utils'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useEffect, useState } from 'react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import DepartmentChip from '@/components/ui/department-chip'
import TaskActions from './task-components/task-actions'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { invalidateActivityLogs } from '@/services/invalidate-query.service'

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
interface Assignee {
    id: number
    first_name: string
    last_name: string
    img_url?: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
}

export default function TaskPage() {
    const params = useParams()
    const taskId = Number(params.id)
    const router = useRouter()
    const { setBreadcrumbs } = useBreadcrumbStore()

    const [linkCopied, setLinkCopied] = useState(false)

    const copyShortCode = (short_code: string) => {
        navigator.clipboard.writeText(short_code || '')
        setLinkCopied(true)
        setTimeout(() => setLinkCopied(false), 3000)
        toast.success('Copied to clipboard!')
    }

    const {
        data: task,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['task', taskId],
        queryFn: async () => {
            try {
                const response = await api.get(`${endpoints.tasks.getTasks}/${taskId}`)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch task details')
                router.push('/app/planner')
                throw error
            }
        },
        enabled: !!taskId,
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Planner', href: '/app/planner' },
            { label: task?.short_code || 'task Name', href: `/app/task/${taskId}` },
        ])
    }, [setBreadcrumbs, task?.short_code, taskId])

    const taskStatus = task?.taskStatus.label
    const taskDueDate = task?.due_date
    const taskDueInfo = getDateDifference(taskDueDate)

    const onAftedDeleteSuccess = () => {
        toast.success('Task deleted successfully')
        router.push('/app/planner')
    }

    const refetchTask = () => {
        refetch()
        invalidateActivityLogs()
    }

    const showDeadlineAlert = taskDueInfo && taskStatus?.toLowerCase() !== 'complete' && taskDueDate && taskDueInfo?.daysLeft <= 2

    const updateDefaultValues = {
        shortCode: task?.short_code,
        taskId: task?.id,
        taskName: task?.title,
        project: String(task?.feature?.project?.id),
        feature: String(task?.feature?.id),
        teamMembers: task?.taskAssignees?.map((assignee: Assignee) => assignee?.id),
        department: String(task?.taskDepartment?.id),
        status: String(task?.taskStatus?.id),
        priority: String(task?.taskPriority?.id),
        dueDate: new Date(task?.due_date),
        description: task?.description,
        isBug: task?.bug,
        parentTaskId: task?.parentTasks?.map((task: DependentTaskType) => task?.short_code).join(','),
        projectName: task?.feature?.project.name,
        featureName: task?.feature?.title,
        assignees: task?.taskAssignees,
        departmentName: task?.taskDepartment?.label,
        statusName: task?.taskStatus?.label,
        priorityName: task?.taskPriority?.label,
        taskassigneesArray: task?.taskAssignees,
    }

    const updateCriteria = async (data: { task_id: number; acceptance_criteria: { id: number; is_checked: boolean }[] }) => {
        const response = await api.put(endpoints.tasks.updateTask + '/' + taskId, {
            task_id: taskId,
            acceptance_criteria: data.acceptance_criteria,
        })
        return response.data
    }

    const updateCriteriaMutation = useMutation({
        mutationFn: updateCriteria,
        onSuccess: () => {
            toast.success('Criteria updated successfully')
            refetchTask()
            invalidateActivityLogs()
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update criteria')
        },
    })

    const handleCriteriaChange = (criteriaId: number, checked: boolean) => {
        updateCriteriaMutation.mutate({
            task_id: taskId,
            acceptance_criteria: task?.acceptance_criteria.map(
                (criteriaObj: { id: number; criteria: string; is_checked: boolean }) => {
                    if (criteriaObj.id === criteriaId) {
                        return { ...criteriaObj, is_checked: checked }
                    }
                    return criteriaObj
                },
            ),
        })
    }

    if (isLoading)
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )

    return (
        <ScrollArea className="h-[90dvh] mt-3" type="scroll">
            <div className="w-full space-y-2">
                {showDeadlineAlert && (
                    <DeadlineAlert
                        title={`You're Almost There! Deadline ${taskDueInfo?.label}`}
                        description={`Great progress! You're in the final stretch. Remember, your task deadline is ${taskDueInfo?.label}. Review your tasks and let's finish strong!`}
                        className="bg-gradient-to-r from-[#F3F3F3] via-[#F4F5F6] to-[#F8F8F8] text-white p-4"
                    />
                )}
                <div className="flex gap-4">
                    <div className="w-full space-y-2 border bg-white rounded-[10px] pt-2 pb-4 px-6">
                        <div className="flex justify-between items-start h-fit overflow-hidden mb-0">
                            <div className="flex items-center gap-4 pt-1">
                                <button
                                    onClick={() => copyShortCode(task?.short_code)}
                                    className={linkCopied ? 'opacity-50 cursor-not-allowed' : 'cursor-copy'}
                                    disabled={linkCopied}>
                                    <DepartmentChip
                                        shortCode={task?.taskDepartment?.short_code || ''}
                                        label={task?.short_code || ''}
                                        size="sm"
                                    />
                                </button>
                                {task?.isDelayed && (
                                    <Badge variant="default" className="flex items-center gap-1 text-[#EC6A5B] bg-[#FDEFEE]">
                                        <OctagonAlert size={15} /> <p className="text-sm">Delayed</p>
                                    </Badge>
                                )}
                                {task?.bug && (
                                    <div className="flex items-center gap-1 text-[#FF6D00]">
                                        <Bug size={15} /> <p className="text-sm">Bug</p>
                                    </div>
                                )}
                            </div>
                            <div className="max-h-10">
                                <TaskActions
                                    updateDefaultValues={updateDefaultValues}
                                    refetchTask={refetchTask}
                                    taskData={task}
                                    onAftedDeleteSuccess={onAftedDeleteSuccess}
                                />
                            </div>
                        </div>
                        {task?.dependentTasks?.length > 0 && <DependentTaskAccordian dependentTasks={task?.dependentTasks} />}
                        <Card className="gap-0 px-0 py-2 rounded-[10px] shadow-none border-none -mt-3 border">
                            <CardHeader className="font-medium text-3xl px-0 pb-2">{task?.title}</CardHeader>
                            <CardContent className="text-sm text-[#71717A] px-0 text-justify pr-4">
                                <MarkdownRenderer content={task?.description} allowCopy={false} enableExpansion={false} />
                            </CardContent>
                        </Card>
                        {task?.acceptance_criteria?.length > 0 && (
                            <div>
                                <p className="text-md font-semibold">Acceptance Criteria</p>
                                {task?.acceptance_criteria?.map(
                                    (criteriaObj: { id: number; criteria: string; is_checked: boolean }, index: number) => (
                                        <div key={index} className="mt-4 flex items-center gap-3 ">
                                            <Checkbox
                                                id={`criteria-${criteriaObj?.id}`}
                                                checked={criteriaObj.is_checked || false}
                                                onCheckedChange={(checked) => handleCriteriaChange(criteriaObj.id, !!checked)}
                                                disabled={updateCriteriaMutation.isPending}
                                            />
                                            <label
                                                htmlFor={`criteria-${criteriaObj.id}`}
                                                className={`text-sm font-medium cursor-pointer ${
                                                    criteriaObj.is_checked ? 'text-[#71717A]' : ''
                                                }`}>
                                                {criteriaObj.criteria}
                                            </label>
                                        </div>
                                    ),
                                )}
                            </div>
                        )}
                    </div>
                    {task && <TaskDetailsCard taskData={task} refetchTask={refetchTask} />}
                </div>
                <TaskBottomTabs />
            </div>
        </ScrollArea>
    )
}
