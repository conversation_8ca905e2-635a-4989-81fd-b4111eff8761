'use client'

import * as React from 'react'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { Search } from 'lucide-react'
import debounce from 'lodash/debounce'

interface DebouncedSearchProps extends React.InputHTMLAttributes<HTMLInputElement> {
    onSearchEnd: (value: string) => void
    delay?: number
    className?: string
    placeholder?: string
    ref?: React.Ref<HTMLInputElement> | null
}

export function DebouncedSearch({
    onSearchEnd,
    delay = 500,
    className,
    placeholder = 'Search...',
    ...props
}: DebouncedSearchProps) {
    const [value, setValue] = React.useState<string>('')

    // Create a debounced search function using Lodash
    const debouncedSearch = React.useMemo(
        () =>
            debounce((searchValue: string) => {
                onSearchEnd(searchValue)
            }, delay),
        [delay, onSearchEnd],
    )

    // Cleanup the debounced function on unmount
    React.useEffect(() => {
        return () => {
            debouncedSearch.cancel()
        }
    }, [debouncedSearch])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const searchValue = e.target.value
        setValue(searchValue)
        debouncedSearch(searchValue)
    }

    return (
        <div className={cn('relative ', className)}>
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input {...props} value={value} onChange={handleChange} placeholder={placeholder} className="pl-8 h-full" />
        </div>
    )
}
