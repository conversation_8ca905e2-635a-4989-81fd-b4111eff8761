'use client'

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Content, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import * as React from 'react'

export default function SkillProgressBar({ value }: { value: number }) {
    const width = `${value}%`
    const showHandle = value > 0 && value < 100

    // Calculate which step indicators should be hidden based on handle position
    const getIndicatorVisibility = (index: number) => {
        if (!showHandle) return true

        // Each step represents 25% (100% / 4 intervals)
        const stepPercentage = (index / 4) * 100
        const handlePosition = value

        // Hide indicator if handle is within ±5% of the step position
        return Math.abs(handlePosition - stepPercentage) > 5
    }

    return (
        <div className="w-full max-w-2xl mx-auto">
            <p className="text-xs font-medium">Skill Level</p>

            <div className="relative mt-3">
                {/* Background track */}
                <div className="bg-[#F5F7FA] absolute top-1/2 h-[20px] left-0 right-0 rounded-full -translate-y-1/2" />

                {/* Gradient bar */}
                <div className="relative top-1/2 h-[20px]" style={{ width: width, zIndex: 10 }}>
                    <div
                        className={cn(
                            'absolute top-1/2 h-[20px] left-0 rounded-l-full -translate-y-1/2 bg-gradient-to-r from-indigo-400 via-sky-400 to-cyan-400',
                            !showHandle && 'rounded-full',
                        )}
                        style={{ width: '100%', zIndex: 10 }}
                    />

                    {/* Handle (half under the bar) */}
                    {showHandle && (
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div
                                    className="absolute h-[32px] w-[32px] bg-white border border-gray-300 rounded-[8px] -right-3 -translate-y-1/2 z-0"
                                    style={{ top: '50%' }}
                                />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="text-sm"> {value}</p>
                            </TooltipContent>
                        </Tooltip>
                    )}
                </div>
                {/* Step indicators */}
                <div className="absolute top-1/2 left-0 right-0 flex px-2 justify-between -translate-y-1/2 z-20">
                    {[0, 1, 2, 3, 4].map((index) => (
                        <span
                            key={index}
                            className={cn(
                                'h-2 w-2 rounded-full border-white shadow transition-opacity duration-200',
                                index === 4 ? 'bg-[#99A0AE]' : 'bg-white',
                                getIndicatorVisibility(index) ? 'opacity-100' : 'opacity-0',
                            )}
                        />
                    ))}
                </div>
            </div>

            {/* Labels */}
            <div className="mt-4 flex justify-between text-xs text-[#717784]">
                <span>Novice</span>
                <span>Pro</span>
                <span>Elite</span>
            </div>
        </div>
    )
}
