'use client'

import React, { useState } from 'react'
import { useTheme } from 'next-themes'
import Markdown from 'react-markdown'
import { Copy, ExpandIcon, MinimizeIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'

interface MarkdownRendererProps {
    content: string
    className?: string
    enableExpansion?: boolean
    initialExpanded?: boolean
    maxPreviewLength?: number
    allowCopy?: boolean
}

export default function MarkdownRenderer({
    content,
    className = '',
    enableExpansion = true,
    initialExpanded = true,
    maxPreviewLength = 200,
    allowCopy = true,
}: MarkdownRendererProps) {
    const [isExpanded, setIsExpanded] = useState(initialExpanded)
    const { theme } = useTheme()

    // Handle copying text to clipboard
    const handleCopy = async (codeContent: string | string[]) => {
        const text = Array.isArray(codeContent) ? codeContent.join('') : codeContent
        try {
            await navigator.clipboard.writeText(text)
            toast.success('Copied to clipboard!')
        } catch (err) {
            console.error('Failed to copy:', err)
        }
    }

    // Check if content is long enough to warrant expansion controls
    const isLongContent = enableExpansion && content.length > 500
    const displayContent = isLongContent && !isExpanded ? content.substring(0, maxPreviewLength) + '...' : content

    return (
        <div className={className}>
            {/* Action buttons */}
            {allowCopy && (
                <div className="flex flex-row justify-end items-center gap-2">
                    <button className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700" onClick={() => handleCopy(content)}>
                        <Copy size={14} />
                    </button>
                </div>
            )}
            <div className={`markdown-content ${!isExpanded && isLongContent ? 'max-h-32 overflow-hidden relative' : ''}`}>
                <Markdown
                    components={{
                        h1: ({ ...props }) => <h1 className="text-2xl font-bold my-4" {...props} />,
                        h2: ({ ...props }) => <h2 className="text-xl font-semibold my-3" {...props} />,
                        h3: ({ ...props }) => <h3 className="text-lg font-medium my-2" {...props} />,
                        p: ({ ...props }) => <p className="text-[15px] leading-relaxed mb-2" {...props} />,
                        ul: ({ ...props }) => <ul className="text-[15px] list-disc pl-6 mb-2" {...props} />,
                        ol: ({ ...props }) => <ol className="text-[15px] list-decimal pl-6 mb-2" {...props} />,
                        li: ({ ...props }) => <li className="text-[15px] mb-1" {...props} />,
                        a: ({ ...props }) => <a className="text-[15px] text-blue-500 hover:underline" {...props} />,
                        code: ({
                            className,
                            children,
                            ...props
                        }: {
                            inline?: boolean
                            className?: string
                            children?: React.ReactNode
                        }) => {
                            const match = /language-(\w+)/.exec(className || '')
                            const language = match ? match[1] : ''
                            const isInline = props.inline !== false && !language

                            return !isInline ? (
                                // Block code (with or without language)
                                <div className="relative group my-4 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-900">
                                    {language ? (
                                        <SyntaxHighlighter
                                            style={theme === 'dark' ? oneDark : oneLight}
                                            language={language}
                                            PreTag="div"
                                            customStyle={{
                                                margin: 0,
                                                padding: '16px',
                                                fontSize: '14px',
                                                lineHeight: '1.5',
                                                fontFamily:
                                                    'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                                                background: 'transparent',
                                            }}>
                                            {String(children).replace(/\n$/, '')}
                                        </SyntaxHighlighter>
                                    ) : (
                                        <pre
                                            className="p-4 text-sm overflow-x-auto"
                                            style={{
                                                fontFamily:
                                                    'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                                                margin: 0,
                                            }}>
                                            {children}
                                        </pre>
                                    )}
                                    <button
                                        onClick={() => handleCopy(String(children))}
                                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-200 dark:bg-gray-700 p-1 rounded text-xs">
                                        <Copy size={14} />
                                    </button>
                                </div>
                            ) : (
                                // Inline code
                                <code
                                    className="inline-flex items-center px-1.5 py-0.5 mx-0.5 rounded text-sm bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
                                    style={{
                                        fontFamily:
                                            'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                                        fontSize: '13px',
                                    }}
                                    {...props}>
                                    {children}
                                </code>
                            )
                        },
                        pre: ({ children }) => <>{children}</>, // Just pass through, let code handle it
                        blockquote: ({ ...props }) => (
                            <blockquote className="border-l-4 border-gray-300 pl-4 italic my-2" {...props} />
                        ),
                    }}>
                    {displayContent}
                </Markdown>
            </div>

            {/* Show fade effect and expand button for long content */}
            {isLongContent && (
                <div className={`w-full flex justify-center mt-2 ${!isExpanded ? 'relative' : ''}`}>
                    {!isExpanded && (
                        <div className="absolute bottom-8 left-0 w-full h-12 bg-gradient-to-t from-gray-100 dark:from-gray-800 to-transparent"></div>
                    )}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="text-xs flex items-center gap-1">
                        {isExpanded ? (
                            <>
                                <MinimizeIcon className="h-3 w-3" />
                                Show Less
                            </>
                        ) : (
                            <>
                                <ExpandIcon className="h-3 w-3" />
                                Show More
                            </>
                        )}
                    </Button>
                </div>
            )}
        </div>
    )
}
