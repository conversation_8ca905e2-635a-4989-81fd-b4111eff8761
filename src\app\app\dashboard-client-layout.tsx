'use client'

import { ReactNode } from 'react'
import { AppSidebar } from '@/components/app-sidebar'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'

import { usePathname } from 'next/navigation'
import { Home, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useAIAgent } from '@/hooks/use-ai-agent'
import { useGlobalState } from '@/store/global-states.store'
import NotificationBell from '@/components/notifications'
import { cn } from '@/lib/utils'
import { useIsMobile } from '@/hooks/use-mobile'
import { useAuthStore } from '@/store/auth.store'

// const tabItems = [
//     {
//         value: 'chat',
//         label: 'Chat',
//         activeIcon: <Image alt="" src="/assets/icons/chat-active.svg" width={16} height={16} />,
//         inactiveIcon: <Image alt="" src="/assets/icons/chat-inactive.svg" width={16} height={16} />,
//     },
//     {
//         value: 'history',
//         label: 'History',
//         activeIcon: <GalleryVerticalEnd fill="#2A2A2A" />,
//         inactiveIcon: <GalleryVerticalEnd color="#B5B5B5" fill="#B5B5B5" />,
//     },
// ]

export default function ClientLayout({ children, defaultOpen }: { children: ReactNode; defaultOpen: boolean }) {
    const pathname = usePathname()
    const { resetProject, hasMessages, isLoading } = useAIAgent()
    const { setActiveSession } = useGlobalState()
    const { currentWorkspace, user } = useAuthStore()
    const isMobile = useIsMobile()
    const { logout } = useAuthStore()

    const handlePlusClick = () => {
        resetProject()
        setActiveSession(null)
    }

    const renderHeader = () => {
        if (pathname.includes('/chat') || pathname === '/app') {
            return (
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear">
                    <div className="flex items-center gap-2 px-4 w-full">
                        <SidebarTrigger className="-ml-1" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <div className="flex justify-between bg-transparent p-4  w-full mt-2">
                            <div className="w-1/3"></div>
                            <div className="flex justify-center items-center w-1/3">
                                <div className="text-sm  ml-[-130px]">Raydian</div>
                            </div>
                            <div className="flex justify-center gap-2 items-center w-1/3 "></div>
                        </div>
                    </div>
                </header>
            )
        }

        return (
            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                <div className="flex justify-between items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <BreadcrumbComponent />
                </div>
                {/* <NotificationBell userId={user?.id} workspaceId={currentWorkspace?.id} /> */}
            </header>
        )
    }

    const handleHomeClick = async () => {
        try {
            await logout()
            window.location.href = '/sign-in'
        } catch (error) {
            console.error('Failed to logout:', error)
        }
    }

    if (isMobile) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-6">
                <div className="text-center max-w-md">
                    <div className="mb-6">
                        <div className="text-2xl font-semibold text-gray-900 mb-2">Desktop Only</div>
                        <div className="text-gray-600">
                            Raydian is currently available only on desktop devices for the best experience.
                        </div>
                    </div>
                    <div className="text-sm text-gray-500">Please access the app from a desktop or laptop computer.</div>
                    <div className="flex justify-center">
                        <Button
                            size="icon"
                            variant="outline"
                            className="mt-4 px-4 py-2 text-white rounded hover:bg-gray-800"
                            onClick={handleHomeClick}>
                            <Home size={16} color="#000" />
                        </Button>
                    </div>
                </div>
            </div>
        )
    }
    return (
        <SidebarProvider defaultOpen={defaultOpen}>
            <AppSidebar />
            <SidebarInset className="overflow-hidden">
                <div className="w-full relative">
                    {renderHeader()}
                    <div
                        className={cn(
                            'absolute top-[18px] ',
                            pathname.includes('/chat') || pathname === '/app' ? 'right-30' : 'right-10',
                        )}>
                        {(pathname.includes('/chat') || pathname === '/app') && (
                            <Button
                                onClick={handlePlusClick}
                                size="icon"
                                className=" rounded-lg p-2 mr-2"
                                title="New Chat"
                                disabled={!hasMessages || isLoading}>
                                <Plus size={16} color="#fff" />
                            </Button>
                        )}
                        <NotificationBell userId={user?.id} workspaceId={currentWorkspace?.id} />
                    </div>
                </div>
                {children}
            </SidebarInset>
        </SidebarProvider>
    )
}

function BreadcrumbComponent() {
    const { breadcrumbs } = useBreadcrumbStore()

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {breadcrumbs.map((crumb, index) => (
                    <div key={index} className="flex flex-row items-center gap-4">
                        <BreadcrumbItem className="hidden md:block">
                            <BreadcrumbLink href={crumb.href}>{crumb.label}</BreadcrumbLink>
                        </BreadcrumbItem>
                        {index < breadcrumbs.length - 1 && <BreadcrumbSeparator className="hidden md:block" />}
                    </div>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    )
}
