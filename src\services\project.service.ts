import { api } from '@/config/axios-config'
import { Project } from '@/types/project'

// const API_URL = process.env.NEXT_PUBLIC_API_URL

/**
 * Save project with generated tasks to the database
 * @param {string} projectName - Project name
 * @param {string} projectDescription - Project description
 * @param {number} workspaceId - Workspace ID
 * @param {Array} tasks - Tasks array
 * @param {Object} techStack - Tech stack information
 * @returns {Promise} - Promise with response
 */
interface Task {
    id: string
    title: string
    priority: string
    description: string
    category: string
    estimatedHours: number
    assignedRole: string
    dependencies: string[]
    acceptanceCriteria: string[]
}

interface TechStack {
    [key: string]: string
}

interface SaveProjectResult {
    status: 'success' | 'error'
    message: string
    data?: {
        project: {
            id: string | number
            name: string
            workspace_id: string | number
            isNew?: boolean
        }
        feature: {
            id: string | number
            code: string
        }
        taskCount: number
    }
}

export const saveProject = async (
    userId: number | undefined,
    featureSummary: string,
    workspaceId: number,
    tasks: Task[],
    techStack: TechStack = {},
    existingProjectId?: string | null,
): Promise<SaveProjectResult | undefined> => {
    try {
        const response = await api.post(
            `/project/save`,
            {
                userId,
                featureSummary,
                workspaceId,
                tasks,
                techStack,
                existingProjectId: existingProjectId || undefined,
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        )

        return response.data
    } catch (error: unknown) {
        console.error('Error saving project:', error)
    }
}

export const getAllProjects = async (workspaceId?: string | number, searchQuery?: string): Promise<Project[]> => {
    try {
        const params = new URLSearchParams()
        if (workspaceId) params.append('workspace_id', workspaceId.toString())
        if (searchQuery) params.append('searchQuery', searchQuery)

        const response = await api.get(`/project?${params.toString()}`)
        return response.data.data.projectData || []
    } catch (error) {
        console.error('Error fetching projects:', error)
        throw error
    }
}
