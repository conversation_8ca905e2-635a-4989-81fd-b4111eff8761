name: Deploy Frontend to GHCR and VPS

on:
    push:
        branches:
            - main
    workflow_dispatch:

jobs:
    deploy:
        runs-on: ubuntu-latest
        env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

        steps:
            - name: Notify In Slack When Starting
              uses: act10ns/slack@v2
              with:
                  status: starting
                  config: .github/slack.yml
                  channel: '#deployments'
              if: always()

            - name: Checkout Code
              uses: actions/checkout@v3

            - name: Log in to GitHub Container Registry
              uses: docker/login-action@v2
              with:
                  registry: ghcr.io
                  username: ${{ github.actor }}
                  password: ${{ secrets.IMAGE_PUSH_TOKEN }}

            - name: Build and Push Frontend Image
              run: |
                  docker build \
                    --build-arg NEXT_PUBLIC_API_URL=${{ secrets.NEXT_PUBLIC_API_URL }} \
                    --build-arg NEXT_PUBLIC_MINIO_ACCESS_KEY=${{ secrets.NEXT_PUBLIC_MINIO_ACCESS_KEY }} \
                    --build-arg NEXT_PUBLIC_MINIO_SECRET_KEY=${{ secrets.NEXT_PUBLIC_MINIO_SECRET_KEY }} \
                    --build-arg NEXT_PUBLIC_MINIO_BUCKET_NAME=${{ secrets.NEXT_PUBLIC_MINIO_BUCKET_NAME }} \
                    --build-arg NEXT_PUBLIC_MINIO_ENDPOINT=${{ secrets.NEXT_PUBLIC_MINIO_ENDPOINT }} \
                    --build-arg NEXT_PUBLIC_MINIO_PORT=${{ secrets.NEXT_PUBLIC_MINIO_PORT }} \
                    --build-arg NEXT_PUBLIC_MINIO_USE_SSL=${{ secrets.NEXT_PUBLIC_MINIO_USE_SSL }} \
                    --build-arg NEXT_PUBLIC_MINIO_PUBLIC_URL=${{ secrets.NEXT_PUBLIC_MINIO_PUBLIC_URL }} \
                    --build-arg NEXT_PUBLIC_GA_MEASUREMENT_ID=${{ secrets.NEXT_PUBLIC_GA_MEASUREMENT_ID }} \
                    -t ghcr.io/beurokrat/kaizen:latest .

                  docker push ghcr.io/beurokrat/kaizen:latest

            - name: Deploy Frontend to VPS via SSH
              uses: appleboy/ssh-action@v0.1.6
              with:
                  host: ${{ secrets.VPS_HOST }}
                  username: ${{ secrets.VPS_USER }}
                  key: ${{ secrets.VPS_PRIVATE_KEY }}
                  script: |
                      docker pull ghcr.io/beurokrat/kaizen:latest || true
                      docker-compose down || true
                      docker-compose up -d --build

            - name: Notify In Slack After Completion
              uses: act10ns/slack@v2
              with:
                  status: ${{ job.status }}
                  config: .github/slack.yml
                  channel: '#deployments'
              if: always()
