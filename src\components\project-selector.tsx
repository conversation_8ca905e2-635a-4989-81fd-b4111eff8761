'use client'

import { useState, useEffect } from 'react'
import { Check, FolderIcon, PlusIcon, Loader2, FolderOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useProjectStore } from '@/store/project.store'
import { useAuthStore } from '@/store/auth.store'
import { Project } from '@/types/project'
import CreateProjectModal from './create-project-modal'

interface ProjectSelectorProps {
    onProjectSelect?: (project: Project | null) => void
    onCreateNew?: () => void
    className?: string
    disableSelect?: boolean
}

export const ProjectSelector: React.FC<ProjectSelectorProps> = ({
    onProjectSelect,
    onCreateNew,
    className = '',
    disableSelect = false,
}) => {
    const [open, setOpen] = useState(false)
    const [openCreateProject, setOpenCreateProject] = useState(false)

    const { availableProjects, selectedProject, isLoadingProjects, setSelectedProject, fetchAvailableProjects } =
        useProjectStore()

    const { getActiveWorkspaceId } = useAuthStore()
    const workspaceId = getActiveWorkspaceId()

    // Fetch projects once when component mounts
    useEffect(() => {
        if (!workspaceId || !open) return
        fetchAvailableProjects(workspaceId?.toString())
    }, [workspaceId, fetchAvailableProjects, open])

    const handleProjectSelect = (project: Project) => {
        if (disableSelect) return
        setSelectedProject(project)
        onProjectSelect?.(project)
        setOpen(false)
    }

    const handleCreateNew = () => {
        setSelectedProject(null)
        onCreateNew?.()
        setOpen(false)
        setOpenCreateProject(true)
    }

    const handleCloseCreateProjectModal = (data: Project | null) => {
        if (data) {
            setSelectedProject(data)
        }
        setOpenCreateProject(false)
    }

    return (
        <div className={className}>
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        size="sm"
                        role="combobox"
                        aria-expanded={open}
                        className="flex items-center gap-2 justify-between rounded-[11px] border-[#5ABD6130]">
                        <div className="flex items-center gap-2">
                            <FolderOpen className="h-4 w-4" color="#9BA5B1" />
                            <span className="text-xs font-medium truncate max-w-[80px] text-[#585858]">
                                {selectedProject ? selectedProject.name : 'Project'}
                            </span>
                        </div>
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="start">
                    <Command>
                        <CommandInput placeholder="Search projects..." className="h-9" />
                        <CommandList>
                            <CommandEmpty>
                                {isLoadingProjects ? (
                                    <div className="flex items-center justify-center p-6">
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        <span className="text-sm text-muted-foreground">Loading projects...</span>
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center p-6">
                                        <FolderIcon className="h-8 w-8 text-muted-foreground/50 mb-2" />
                                        <span className="text-sm font-medium">No projects found</span>
                                        <span className="text-xs text-muted-foreground mt-1">
                                            Create your first project to get started
                                        </span>
                                    </div>
                                )}
                            </CommandEmpty>

                            <CommandGroup heading="Actions">
                                <CommandItem disabled={disableSelect} onSelect={handleCreateNew} className="text-primary">
                                    <div className="flex items-center gap-3 w-full">
                                        <div className="p-1 bg-primary/10 rounded">
                                            <PlusIcon className="h-4 w-4 text-primary" />
                                        </div>
                                        <div className="flex-1">
                                            <div className="font-medium">Create New Project</div>
                                            <div className="text-xs text-muted-foreground">Start fresh with a new project</div>
                                        </div>
                                    </div>
                                </CommandItem>
                            </CommandGroup>

                            {availableProjects.length > 0 && (
                                <>
                                    <Separator className="my-1" />
                                    <CommandGroup heading={`Available Projects (${availableProjects.length})`}>
                                        {availableProjects.map((project: Project) => (
                                            <CommandItem
                                                disabled={disableSelect}
                                                key={project.id}
                                                onSelect={() => handleProjectSelect(project)}
                                                className="p-3">
                                                <div className="flex items-start gap-3 w-full">
                                                    <div className="p-1 bg-blue-100 dark:bg-blue-900/40 rounded mt-0.5">
                                                        <FolderIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center justify-between mb-2">
                                                            <h4 className="font-medium truncate">{project.name}</h4>
                                                            <div className="flex items-center gap-2">
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {project.progress?.percentage}%
                                                                </Badge>
                                                                {selectedProject?.id === project.id && (
                                                                    <Check className="h-4 w-4 text-primary" />
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                                                            {/* <Badge variant="outline" className="text-xs">
                                                                {project.workspace.name}
                                                            </Badge> */}
                                                            <span>
                                                                {project.progress?.completed}/{project.progress?.total} tasks
                                                            </span>
                                                            {project.features?.total && project.features.total > 0 && (
                                                                <span>• {project.features?.total} features</span>
                                                            )}
                                                        </div>
                                                        {project.description && (
                                                            <p className="text-xs text-muted-foreground line-clamp-2">
                                                                {project.description}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            </CommandItem>
                                        ))}
                                    </CommandGroup>
                                </>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
            <CreateProjectModal
                isOpen={openCreateProject}
                onClose={(data: Project | null) => handleCloseCreateProjectModal(data)}
                reFetchProjectList={() => fetchAvailableProjects(workspaceId?.toString())}
            />
        </div>
    )
}
