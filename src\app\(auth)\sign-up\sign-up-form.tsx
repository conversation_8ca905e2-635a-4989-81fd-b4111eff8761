'use client'

import React, { useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Form, FormField } from '@/components/ui/form'

import { Loader } from 'lucide-react'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { api } from '@/config/axios-config'
import { useRouter, useSearchParams } from 'next/navigation'
import FormOverlayWrapper from '@/components/form-overlay-wrapper'
import { FormInputField, PasswordInputField } from '@/components/form-fields'
import { useAuthStore } from '@/store/auth.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { splitFullName } from '@/utils/split-full-name.utils'

// Extend the schema to include confirmPassword validation
const formSchema = z.object({
    fullName: z
        .string()
        .min(1, 'Full name is required')
        .refine((val) => val.trim().split(/\s+/).length >= 2, {
            message: 'Please enter a valid full name with first and last name',
        }),
    email: z.string().email('Invalid email'),
    password: z
        .string()
        .min(8, 'Password must be at least 8 characters long')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
})

const SignUpForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const redirectUrl = searchParams.get('redirect')
    const { setInvitationUrl } = useAuthStore()

    useEffect(() => {
        setInvitationUrl(redirectUrl)
    }, [redirectUrl, setInvitationUrl])
    const setPendingVerificationEmail = useAuthStore((state) => state.setPendingVerificationEmail)
    // React Hook Form setup
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
            password: '',
            fullName: '',
        },
    })

    // React Query mutation for form submission
    const mutation = useMutation({
        mutationFn: async (values: z.infer<typeof formSchema>) => {
            const { firstName, lastName } = splitFullName(values.fullName)
            const body = {
                userType: 1,
                confirmPassword: values.password,
                firstName,
                lastName,
                email: values.email,
                password: values.password,
            }
            const response = await api.post(endpoints.authentication.signUp, body)
            return response.data
        },
        onSuccess: (data) => {
            toast.success('Verification email sent successfully.')
            setPendingVerificationEmail(data.email)
            if (redirectUrl) {
                router.push(`/verify-otp?redirect=${redirectUrl}`)
                return
            }
            router.push('/verify-otp')
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to send verification email. Please try again.')
        },
    })

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Sending otp...') // Show loading toast
        mutation.mutate(values, {
            onSettled: () => {
                toast.dismiss(toastId) // Dismiss the loading toast when the mutation is settled
            },
        })
    }
    const handleGoogleLogin = () => {
        window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/auth/google`
    }
    const handleMicrosoftLogin = () => {
        window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/auth/microsoft`
    }
    return (
        <div>
            <div className="flex mx-auto max-w-[380px]">
                <FormOverlayWrapper isSubmitting={mutation.isPending}>
                    <Card className="w-[380px] max-w-xl border shadow-none bg-[#FDFDFD] rounded-3xl">
                        <CardHeader>
                            <CardTitle className="text-2xl py-1">Let&apos;s get started..</CardTitle>
                        </CardHeader>

                        <CardContent>
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                    <FormField
                                        control={form.control}
                                        name="fullName"
                                        render={({ field }) => (
                                            <FormInputField label="Full name *" placeholder="Full name" field={field} />
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormInputField label="Email *" placeholder="Your email" type="email" field={field} />
                                        )}
                                    />
                                    {/* Password Field */}
                                    <FormField
                                        control={form.control}
                                        name="password"
                                        render={({ field }) => (
                                            <PasswordInputField label="Password *" placeholder="Your password" field={field} />
                                        )}
                                    />

                                    <Button type="submit" className="w-full rounded-sm mt-2" disabled={mutation.isPending}>
                                        {mutation.isPending ? <Loader className="animate-spin" /> : 'Create an account'}
                                    </Button>
                                </form>
                            </Form>

                            {/* OR separator and social logins */}
                            <div className="text-center space-y-4 mt-6">
                                <div className="my-4 flex items-center">
                                    <Separator style={{ width: '33%', backgroundColor: '#E9EAEB' }} />
                                    <span className="mx-2 text-xs text-gray-500">or continue with</span>
                                    <Separator style={{ width: '34%', backgroundColor: '#E9EAEB' }} />
                                </div>

                                <div className="flex w-full justify-between">
                                    <Button
                                        variant="outline"
                                        className="w-[48%] bg-[#fff] rounded-sm"
                                        onClick={handleGoogleLogin}>
                                        <div className="flex items-center justify-center w-full">
                                            <Image
                                                src={'/assets/img/GoogleLogo.svg'}
                                                alt="Google Logo"
                                                width={20}
                                                height={20}
                                                className="mr-2"
                                            />
                                        </div>
                                    </Button>

                                    <Button
                                        variant="outline"
                                        className="w-[48%] bg-[#fff] rounded-sm"
                                        onClick={handleMicrosoftLogin}>
                                        <div className="flex items-center justify-center w-full pl-2">
                                            <Image
                                                src={'/assets/img/MsLogo.svg'}
                                                alt="Microsoft Logo"
                                                width={20}
                                                height={20}
                                                className="mr-2"
                                            />
                                        </div>
                                    </Button>
                                </div>

                                <div className="text-[14px] tex-[#0B0B0B]">
                                    Already have an account?{' '}
                                    <Link
                                        href={redirectUrl ? `/sign-in?redirect=${redirectUrl}` : '/sign-in'}
                                        className="underline">
                                        Log In
                                    </Link>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </FormOverlayWrapper>
            </div>
        </div>
    )
}

export default SignUpForm
