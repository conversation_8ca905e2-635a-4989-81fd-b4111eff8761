'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import Image from 'next/image'
import flag from '../../public/assets/icons/flag-project.svg'
import { MultiSelect } from './creatable-multi-select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './ui/form'
import { Project } from '@/types/project'

const formSchema = z.object({
    projectName: z.string().min(1, 'Project name is required'),
    overview: z.string().min(10, 'Overview must be at least 10 characters'),
    techStack: z.array(z.string()).min(1, 'Select at least one technology'),
})

type FormValues = z.infer<typeof formSchema>

interface CreateProjectModalProps {
    isOpen: boolean
    onClose: (data: Project | null) => void
    reFetchProjectList: () => void
}

export default function CreateProjectModal({ isOpen, onClose, reFetchProjectList }: CreateProjectModalProps) {
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            projectName: '',
            overview: '',
            techStack: [],
        },
    })

    const techOptions = [
        'React',
        'Next.js',
        'TypeScript',
        'JavaScript',
        'Tailwind CSS',
        'Node.js',
        'Express',
        'MongoDB',
        'PostgreSQL',
        'GraphQL',
        'Redux',
        'Zustand',
        'React Query',
        'Prisma',
        'Docker',
        'AWS',
        'Firebase',
        'Vercel',
    ]

    const mutation = useMutation({
        mutationFn: async (data: FormValues) => {
            const payload = {
                name: data.projectName,
                overview: data.overview,
                stack: data.techStack,
                workspace_id: currentWorkspace?.id,
                status_id: 1,
            }
            const response = await api.post(endpoints.project.createProject, payload)
            return response.data
        },
        onSuccess: (data) => {
            toast.success('Project created successfully!')
            form.reset()
            reFetchProjectList()
            onClose(data.data)
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to create Project. Please try again.')
        },
    })

    const onSubmit = (data: FormValues) => {
        const toastId = toast.loading('Creating project...')
        mutation.mutate(data, {
            onSettled: () => {
                toast.dismiss(toastId)
            },
        })
    }

    const handleClose = () => {
        form.reset()
        onClose(null)
    }

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
            <div className="bg-white max-w-[60%] rounded-lg w-full p-6 relative">
                <button
                    onClick={handleClose}
                    className="absolute right-8 top-8 text-gray-500 hover:text-black hover:cursor-pointer">
                    <X className="h-6 w-6" />
                </button>

                <div className="mb-6">
                    <div className="h-[48px] w-[48px] flex justify-center items-center rounded-[10px] border mb-2">
                        <Image src={flag} width={24} height={24} alt="folder" />
                    </div>
                    <h2 className="text-lg font-semibold mb-2">Create Project</h2>
                    <p className="text-gray-500 text-sm">Enter Project details to get started</p>
                </div>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="projectName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Project Name*</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="eg Website Design"
                                            className="w-full focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="overview"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Overview*</FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Textarea
                                                placeholder="eg food delivery application where users can search restaurants and place order for delivery"
                                                className="min-h-[120px] max-h-[170px] w-full pt-9 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                                                {...field}
                                            />
                                            <button
                                                type="button"
                                                className="absolute right-2 top-2 text-green-600 text-xs flex items-center border border-gray-100 rounded-md px-2 py-1"
                                                onClick={() => console.log('AI rewrite clicked')}>
                                                <svg
                                                    className="w-4 h-4 mr-1"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12 4V20M20 12H4"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                    />
                                                </svg>
                                                Rewrite with AI
                                            </button>
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="techStack"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-sm text-[#414651] font-semibold">Stack*</FormLabel>
                                    <FormControl>
                                        <MultiSelect
                                            predefinedOptions={techOptions}
                                            defaultSelected={field.value}
                                            onChange={field.onChange}
                                            placeholder="Select tech stack..."
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <Button
                            type="submit"
                            className="w-full bg-gray-800 hover:bg-gray-700 text-white py-6 font-semibold text-md max-h-[43px]"
                            disabled={mutation.isPending}>
                            {mutation.isPending ? 'Creating...' : 'Create Project'}
                        </Button>
                    </form>
                </Form>
            </div>
        </div>
    )
}
