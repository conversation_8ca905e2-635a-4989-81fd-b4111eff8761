'use client'

import { useEffect } from 'react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { Loader } from 'lucide-react'
import UserProfileHeader from './profile-header'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useParams } from 'next/navigation'
import ProfileTabs from './profile-tab'

const Page = () => {
    const { setBreadcrumbs } = useBreadcrumbStore()
    const params = useParams()
    const userId = params.userId

    const { data: user, isLoading } = useQuery({
        queryKey: ['user'],
        queryFn: async () => {
            try {
                const url = endpoints.user.getUserById.replace(':id', userId?.toString() || '')
                const response = await api.get(url)
                return response.data.data
            } catch (error) {
                throw error
            }
        },
    })

    useEffect(() => {
        if (user) {
            setBreadcrumbs([
                { label: 'Dashboard', href: '/app' },
                { label: 'User Management', href: '/app/team-management' },
                { label: `${user?.email}`, href: `/app/team-management/${userId}` },
            ])
        }
    }, [setBreadcrumbs, user, userId])

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-[88dvh]">
                <Loader className="animate-spin" />
            </div>
        )
    }
    return (
        <div className="space-y-30">
            <UserProfileHeader user={user} />
            <div className="max-w-[93%] mx-auto">
                <ProfileTabs user={user} />
            </div>
        </div>
    )
}

export default Page
