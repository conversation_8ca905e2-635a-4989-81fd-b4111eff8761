const getStatusStyles = (label: string) => {
    const lowerLabel = label?.toLowerCase()
    if (!lowerLabel) return
    if (lowerLabel.includes('backlog')) return { bg: 'bg-gray-200', text: 'text-gray-800', border: 'border-gray-300' }
    if (lowerLabel.includes('progress')) return { bg: 'bg-[#FFECD92E]', text: ' text-[#FF8815]', border: 'border-[#FF88151C]' }
    if (lowerLabel.includes('review')) return { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-300' }
    if (lowerLabel.includes('complete')) return { bg: 'bg-green-100', text: ' text-green-800', border: 'border-green-300' }
    if (lowerLabel.includes('delay')) return { bg: 'bg-red-100', text: ' text-red-800', border: 'border-red-300' }
    return { bg: 'bg-gray-100', text: ' text-gray-800', border: 'border-gray-300' }
}

export default getStatusStyles
