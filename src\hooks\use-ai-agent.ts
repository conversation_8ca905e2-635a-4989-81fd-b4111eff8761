import { useCallback, useMemo, useRef, useState, MutableRefObject } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { toast } from 'sonner'
import { useProjectStore } from '@/store/project.store'
import { useAuthStore } from '@/store/auth.store'
import { Message, Task } from '@/types/project'
import { saveProject } from '@/services/project.service'

interface UseAIAgentOptions {
    abortControllerRef?: MutableRefObject<AbortController | null>
}

export const useAIAgent = (options?: UseAIAgentOptions) => {
    const [isLoading, setIsLoading] = useState(false)
    const [isSaving, setIsSaving] = useState(false)
    const accessToken = useAuthStore.getState().accessToken

    // Use external ref if provided, otherwise create internal one
    const internalControllerRef = useRef<AbortController | null>(null)
    const controllerRef = options?.abortControllerRef || internalControllerRef

    const projectStore = useProjectStore()
    const {
        messages,
        tasks,
        projectData,
        projectSession,
        showTasksPanel,
        featureSummary,
        addMessage,
        updateMessage,
        setTasks,
        addTask,
        setProjectSession,
        setShowTasksPanel,
        updateProjectData,
        resetProject: resetProjectFromStore,
    } = projectStore

    const removeMessages = useCallback(
        (messageIds: string[]) => {
            projectStore.removeMessages(messageIds)
        },
        [projectStore],
    )

    const { getUserId, getActiveWorkspaceId, user } = useAuthStore()
    const selectedProject = projectStore.selectedProject ?? null
    const setSelectedProject = useMemo(() => projectStore.setSelectedProject ?? (() => {}), [projectStore.setSelectedProject])

    const parseSSE = async function* (response: Response): AsyncGenerator<string> {
        const reader = response.body?.getReader()
        const decoder = new TextDecoder()
        let buffer = ''
        while (true) {
            const { done, value } = await reader!.read()
            if (done) break
            buffer += decoder.decode(value, { stream: true })
            const parts = buffer.split('\n')
            buffer = parts.pop()!
            for (const line of parts) {
                if (line.startsWith('data: ')) {
                    yield line.slice(6)
                }
            }
        }
    }

    const sendMessage = useCallback(
        async (message: string, isResend?: boolean, messagesToBePreserved?: Message[]) => {
            if (!message.trim() || isLoading) return
            let errorMessage = 'Failed to stream response.'

            const userId = getUserId()
            const sessionId = projectSession || undefined
            const workspaceId = getActiveWorkspaceId()
            if (!projectSession) setProjectSession(sessionId)

            const userMessage: Message = {
                id: uuidv4(),
                content: message,
                parts: [],
                sender: 'user',
                timestamp: new Date(),
            }

            const aiMessageId = uuidv4()
            const aiMessage: Message = {
                id: aiMessageId,
                parts: [],
                content: '',
                sender: 'ai',
                timestamp: new Date(),
            }

            addMessage(userMessage)
            addMessage(aiMessage)
            setIsLoading(true)

            // Abort previous if still active
            if (controllerRef.current) controllerRef.current.abort()
            const controller = new AbortController()
            controllerRef.current = controller

            try {
                const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/ai`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: 'text/event-stream',
                        Authorization: `Bearer ${accessToken}`,
                    },
                    body: JSON.stringify({
                        message,
                        sessionId,
                        selectedProjectId: selectedProject?.id,
                        userId,
                        isResend: isResend,
                        messagesToBePreserved: messagesToBePreserved,
                        workspaceId,
                    }),
                    signal: controller.signal,
                })

                if (!res.ok) {
                    try {
                        const errorData = await res.json()
                        errorMessage = errorData.message || errorData.error || errorMessage
                    } catch {
                        // If response is not JSON, use status text
                        errorMessage = res.statusText || errorMessage
                        throw new Error(errorMessage)
                    }
                }

                if (!res.body) throw new Error('No response body from server.')

                let lastEventWasText = false

                for await (const line of parseSSE(res)) {
                    try {
                        const parsed = JSON.parse(line)

                        if (parsed.sessionId && !projectSession) {
                            setProjectSession(parsed.sessionId)
                            continue
                        }

                        if (parsed.type === 'text' && parsed.content) {
                            if (!lastEventWasText) {
                                aiMessage.parts.push({ type: 'text', content: parsed.content })
                            } else {
                                const lastIndex = aiMessage.parts.length - 1
                                if (lastIndex >= 0 && aiMessage.parts[lastIndex].type === 'text') {
                                    aiMessage.parts[lastIndex].content += parsed.content
                                }
                            }
                            updateMessage(aiMessageId, (prev) => prev + parsed.content)
                            lastEventWasText = true
                        }

                        if (parsed.type === 'chips' && parsed.content) {
                            aiMessage.parts.push({ type: 'chips', content: parsed.content })
                            updateMessage(aiMessageId, (prev) => prev)
                            lastEventWasText = false
                        }

                        if (parsed.type === 'generate' && parsed.content) {
                            aiMessage.parts.push({ type: 'generate', content: parsed.content })
                            updateMessage(aiMessageId, (prev) => prev)
                            lastEventWasText = false
                        }
                        // Handle search status messages
                        if (parsed.type === 'status' && parsed.content) {
                            aiMessage.parts.push({
                                type: 'status',
                                content: parsed.content,
                            })
                            updateMessage(aiMessageId, () => parsed.content)
                            lastEventWasText = false
                        }
                        if (parsed.message === 'Stream completed' || parsed.message === 'Search completed') {
                            // Clear any status messages from the main content on completion
                            if (
                                aiMessage.content.includes('🔍 Searching the web') ||
                                aiMessage.content.includes('✨ Analyzing results')
                            ) {
                                updateMessage(aiMessageId, () => {
                                    const textParts = aiMessage.parts
                                        .filter((part) => part.type === 'text')
                                        .map((part) => part.content)
                                        .join('')
                                    return textParts
                                })
                            }
                        }
                    } catch (err) {
                        console.warn('Stream parse error:', line, err)
                    }
                }

                updateProjectData()
            } catch (err: unknown) {
                if (err instanceof Error && err.name === 'AbortError') {
                    console.log('Streaming aborted.')
                } else {
                    removeMessages([aiMessageId])
                    addMessage({
                        id: uuidv4(),
                        content: '⚠️ Something went wrong while streaming.',
                        parts: [{ type: 'text', content: errorMessage || '⚠️ Something went wrong while streaming.' }],
                        sender: 'ai',
                        timestamp: new Date(),
                    })
                }
            } finally {
                setIsLoading(false)
            }
        },
        [
            projectSession,
            isLoading,
            selectedProject,
            addMessage,
            updateMessage,
            updateProjectData,
            setProjectSession,
            getUserId,
            controllerRef,
            getActiveWorkspaceId,
            accessToken,
            removeMessages,
        ],
    )

    const generateTasks = useCallback(async () => {
        if (!projectSession) {
            toast.error('Please start a conversation first.')
            return
        }
        let errorMessage = 'Failed to generate tasks.'

        setIsLoading(true)
        setTasks([])

        const taskPromptMessage: Message = {
            id: uuidv4(),
            content: 'Generate tasks for this project',
            parts: [],
            sender: 'user',
            timestamp: new Date(),
        }

        const aiMessage: Message = {
            id: uuidv4(),
            content: 'Generating detailed project tasks...',
            parts: [],
            sender: 'ai',
            timestamp: new Date(),
        }

        addMessage(taskPromptMessage)
        addMessage(aiMessage)

        if (controllerRef.current) controllerRef.current.abort()
        const controller = new AbortController()
        controllerRef.current = controller

        try {
            const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/ai`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'text/event-stream',
                    Authorization: `Bearer ${accessToken}`,
                },
                body: JSON.stringify({
                    message: 'generate_tasks',
                    sessionId: projectSession,
                    userId: getUserId(),
                    generateTasks: true,
                }),
                signal: controller.signal,
            })

            if (!res.ok) {
                try {
                    const errorData = await res.json()
                    errorMessage = errorData.message || errorData.error || errorMessage
                } catch {
                    // If response is not JSON, use status text
                    errorMessage = res.statusText || errorMessage
                    throw new Error(errorMessage)
                }
            }

            const generatedTasks: Task[] = []

            for await (const line of parseSSE(res)) {
                const parsed = JSON.parse(line)
                if (parsed.task) {
                    generatedTasks.push(parsed.task)
                    addTask(parsed.task)
                    if (!showTasksPanel) setShowTasksPanel(true)
                } else if (parsed.message === 'Stream completed') {
                    const msg = `I've generated ${generatedTasks.length} tasks for your project!`
                    aiMessage.content = msg
                    aiMessage.parts.push({ type: 'text', content: msg })
                    updateMessage(aiMessage.id, (prev) => prev)
                }
            }
        } catch (error: unknown) {
            if (error instanceof Error && error.name === 'AbortError') {
                console.log('Task generation aborted.')
            } else {
                toast.error('Error generating tasks.')
                removeMessages([aiMessage.id])
                addMessage({
                    id: uuidv4(),
                    content: '⚠️ Something went wrong while generating tasks.',
                    parts: [{ type: 'text', content: errorMessage || '⚠️ Something went wrong while generating tasks.' }],
                    sender: 'ai',
                    timestamp: new Date(),
                })
            }
        } finally {
            setIsLoading(false)
        }
    }, [
        projectSession,
        showTasksPanel,
        addMessage,
        addTask,
        setShowTasksPanel,
        setTasks,
        updateMessage,
        getUserId,
        controllerRef,
        accessToken,
        removeMessages,
    ])

    const publishTasks = useCallback(async () => {
        const workspaceId = getActiveWorkspaceId()
        const userId = getUserId()

        if (!workspaceId) return toast.error('Select a workspace first.')
        if (!user || !userId) return toast.error('Log in to publish tasks.')
        if (!tasks.length) return toast.error('No tasks to publish.')

        const projectName = selectedProject?.name || projectData.name || 'Untitled'
        const existingProjectId = selectedProject?.id?.toString() || null

        setIsSaving(true)

        try {
            const result = await saveProject(userId, featureSummary, workspaceId, tasks, projectData.techStack, existingProjectId)

            if (result?.status === 'success') {
                const isNew = result.data?.project?.isNew ?? true
                toast.success(isNew ? `Project "${projectName}" created!` : `Tasks added to "${projectName}".`)
                setShowTasksPanel(false)
            } else {
                throw new Error(result?.message || 'Save failed.')
            }
        } catch (err) {
            console.error('Save error:', err)
            toast.error('Error saving tasks! You may not have permissions to publish tasks.')
        } finally {
            setIsSaving(false)
        }
    }, [tasks, projectData, selectedProject, user, featureSummary, getActiveWorkspaceId, getUserId, setShowTasksPanel])

    const resetProject = useCallback(() => {
        resetProjectFromStore()
        controllerRef.current?.abort()
    }, [resetProjectFromStore, controllerRef])

    const abortCurrentRequest = useCallback(() => {
        controllerRef.current?.abort()
    }, [controllerRef])

    return {
        messages,
        tasks,
        isLoading,
        isSaving,
        showTasksPanel,
        selectedProject,
        sendMessage,
        generateTasks,
        publishTasks,
        addMessage,
        resetProject,
        setSelectedProject,
        setProjectSession,
        hasMessages: messages.length > 0,
        removeMessages,
        // Expose the controller ref for external access
        abortCurrentRequest,
    }
}
