'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>overContent, PopoverTrigger } from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { ChevronDown, Crown, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useMutation, useQuery } from '@tanstack/react-query'
import endpoints from '@/services/api-endpoints'
import { api } from '@/config/axios-config'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { useAuthStore } from '@/store/auth.store'

interface AccessModifierProps {
    rolesList?: {
        id: number
        label: string
    }[]
    selectedRoles?: {
        id: number
        label: string
    }[]
    userId?: string
    onRoleChangeSuccess?: () => void
}

const dummySelectedRoles = [{ id: 1, label: 'Admin' }]

export const AccessModifier = ({ selectedRoles = dummySelectedRoles, userId, onRoleChangeSuccess }: AccessModifierProps) => {
    const [selected, setSelected] = useState(selectedRoles)

    const { fetchMyDetails, user, currentWorkspace } = useAuthStore()

    const disableSelection = !currentWorkspace?.is_admin

    const { data: rolesList } = useQuery({
        queryKey: ['user-types'],
        queryFn: async () => {
            const response = await api.get(endpoints.meta.getUserTypes)
            return response.data.data
        },
    })

    const updateRoleMutation = useMutation({
        mutationFn: async (roleId: number) => {
            if (!userId) return
            // Replace with your actual API endpoint
            const url = endpoints.workspace.updateUserType
            const response = await api.patch(url, {
                user_type: roleId,
                user_id: userId,
                workspace_id: currentWorkspace?.id,
            })
            return response.data
        },
        onSuccess: () => {
            if (onRoleChangeSuccess) onRoleChangeSuccess()
            if (user?.id === userId) fetchMyDetails() //call this fn because the user type for the current user has changed
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update role. Please try again.')
            // Revert to previous selection on error
            setSelected(selectedRoles)
        },
    })

    const toggleRole = (role: { id: number; label: string }) => {
        if (selected.some((r) => r.id === role.id)) return
        setSelected([role])
        updateRoleMutation.mutate(role.id)
        // if (selected.some((r) => r.id === role.id)) {
        //     setSelected(selected.filter((r) => r.id !== role.id))
        // } else {
        //     setSelected([...selected, role])
        // }
    }

    return (
        <Popover>
            <div className="flex gap-2 items-center" title={disableSelection ? 'Only admins can change access' : ''}>
                {selected.map((role) => (
                    <div key={role.id} className="flex items-center gap-2">
                        {role.label.toLowerCase().includes('admin') ? (
                            <AdminBadge label={role.label} />
                        ) : (
                            <TeamMemberBadge label={role.label} />
                        )}
                    </div>
                ))}
                <PopoverTrigger asChild id="select" disabled={updateRoleMutation.isPending}>
                    <Button variant="outline" size="icon" className="h-6 w-6 p-0">
                        <ChevronDown className="h-4 w-4" />
                    </Button>
                </PopoverTrigger>
            </div>
            <PopoverContent className="w-fit p-1" id="select">
                {rolesList?.map((role: { id: number; label: string }) => (
                    <label
                        key={role.id}
                        className="flex items-center gap-2 py-1 cursor-pointer hover:bg-neutral-100 p-1 rounded-md"
                        id="select">
                        <Checkbox
                            checked={selected.some((r) => r.id === role.id)}
                            onCheckedChange={() => toggleRole(role)}
                            id="select"
                            disabled={disableSelection}
                        />
                        <span id="select" className="text-sm">
                            {role.label}
                        </span>
                    </label>
                ))}
            </PopoverContent>
        </Popover>
    )
}

const commonStyles = 'flex items-center gap-2 border-1 border-white rounded-lg py-1 px-2'

const AdminBadge = ({ label }: { label: string }) => {
    return (
        <div className={cn(commonStyles, 'bg-[#EDE9FE] text-[#7C3AED]')}>
            <Crown size={14} />
            <p>{label}</p>
        </div>
    )
}

const TeamMemberBadge = ({ label }: { label: string }) => {
    return (
        <div className={cn(commonStyles, 'bg-[#DBEAFE] text-[#2563EB]')}>
            <User size={14} />
            <p>{label}</p>
        </div>
    )
}
