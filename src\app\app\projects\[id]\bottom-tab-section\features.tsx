'use client'

import { useParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useTabStore } from '@/store/tabs.store'
import { Calendar, ExternalLink, Loader } from 'lucide-react'
import { formatDate } from '@/utils/format-date.utils'
import Link from 'next/link'
import { FeatureDetailsDrawer } from './modals/feature-data-modal'

type ProjectParams = {
    id: string
}

interface Feature {
    id: number
    title: string
    created_at: string
    feat_code: string
    // Add more fields as needed
}

const FeaturesTab = () => {
    const params = useParams() as ProjectParams
    const projectId = params.id
    const { projectActiveTab } = useTabStore()

    const {
        data: features,
        isLoading,
        refetch: refetchFeaturesOnTab,
    } = useQuery({
        queryKey: ['features', projectId],
        queryFn: async () => {
            try {
                const response = await api.get(endpoints.features.getFeatures, {
                    params: {
                        project_id: projectId,
                    },
                })
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch features')
                throw error
            }
        },
        enabled: !!projectId && projectId !== 'undefined' && projectActiveTab === 'features',
    })

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-4">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <>
            <div className="mb-4">
                <p className="text-[15px] font-medium text-black">Features</p>
                <p className="text-xs text-[#5C5F62]">You can add features and assign to others</p>
            </div>

            {features?.length > 0 ? (
                features.map((feature: Feature) => (
                    <div key={feature.id} className=" text-card-foreground flex items-center gap-4">
                        {/* <p className="text-xs  text-[#5C5F62] w-[10%] line-clamp-1">{feature.feat_code}</p> */}
                        <div className="w-[80%]">
                            <ListItem feature={feature} refetchFeaturesOnTab={refetchFeaturesOnTab} />
                        </div>
                    </div>
                ))
            ) : (
                <div className="text-center text-muted-foreground">No features found for this project</div>
            )}
        </>
    )
}

export default FeaturesTab

const ListItem = ({ feature, refetchFeaturesOnTab }: { feature: Feature; refetchFeaturesOnTab: () => void }) => (
    <div className="flex justify-between items-center mb-3 w-full ">
        <FeatureDetailsDrawer featureId={feature.id} refetchFeaturesOnTab={refetchFeaturesOnTab}>
            <div className="w-[80%]">
                <p className="text-sm font-medium ">{feature.title}</p>
                <div className="flex gap-2 items-center text-[#5C5F62] mt-1">
                    <Calendar height={14} width={14} color="#9BA5B1" />
                    <p className="text-[13px] text-[#5C5F62]">
                        {formatDate(feature.created_at, 'ddMonYYYY', { showTime: true, timeFormat: '12h' })}
                    </p>
                </div>
            </div>
        </FeatureDetailsDrawer>
        <div className="flex gap-2 items-center">
            <Link href={{ pathname: '/app/planner', query: { featureId: feature?.id } }}>
                <div className="flex items-center gap-2 text-[#08B38B]">
                    <p className="text-[12px] font-medium">View in Planner</p>
                    <ExternalLink height={14} width={14} />
                </div>
            </Link>
        </div>
    </div>
)
