import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'

type LogTaskChipProps = {
    entityType?: string
    entityId?: number
    allowNavigation?: boolean
    className?: string
}

const routeMap = {
    tasks: '/app/task/',
} as const

const LogTaskChip = ({
    children,
    entityType = 'tasks',
    entityId,
    className = 'px-2 py-0.5 bg-blue-50 text-blue-700 rounded-full font-medium border border-blue-200 text-xs',
}: React.PropsWithChildren<LogTaskChipProps>) => {
    const route = routeMap[entityType as keyof typeof routeMap] + entityId
    const router = useRouter()
    const allowNavigation = entityId && entityType in routeMap
    const handleClick = () => {
        if (!allowNavigation) return
        router.push(route)
    }
    return (
        <span onClick={handleClick} className={cn(allowNavigation && 'cursor-pointer', className)}>
            {children}
        </span>
    )
}

export default LogTaskChip
