import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
    return {
        rules: [
            {
                userAgent: '*',
                allow: ['/docs/', '/docs/getting-started/', '/docs/guides/', '/docs/api/'],
                disallow: ['/docs/search/', '/docs/internal/'],
            },
            {
                userAgent: 'Googlebot',
                allow: ['/docs/', '/docs/getting-started/', '/docs/guides/', '/docs/api/'],
                disallow: ['/docs/search/', '/docs/internal/'],
            },
        ],
        sitemap: 'https://raydian.ai/docs/sitemap.xml',
    }
}
