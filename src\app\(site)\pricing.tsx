import { Check, User, X, Sparkle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { PurchaseModal } from '@/components/purchase-modal'

const pricingPlans = [
    {
        id: 'free',
        name: 'Free',
        icon: User,
        target: 'For individuals',
        price: 'Free',
        description: 'For Freelancers and Small Teams.',
        text: 'Billed monthly or annually. Cancel anytime.',
        popular: false,
        features: [
            { name: '100 AI prompts included', included: true },
            { name: 'Full access to all features (planner, AI tasks, roadmaps)', included: true },
            { name: '5 Team members', included: true },
            { name: '100 AI Credits', included: true },
            { name: 'No time limits, no credit card required', included: true },
        ],
        cta: (
            <Link href="/sign-in">
                <Button className="w-[282px] text-white py-5 rounded-md text-[14px] font-normal">Get Started</Button>
            </Link>
        ),
    },
    {
        id: 'credits',
        name: 'Credits',
        bg: 'bg-[#0000000F]',
        icon: Sparkle,
        target: 'More power',
        price: 'Upgrade with Credits',
        description: 'For teams who want to scale beyond 100 prompts.',
        text: 'One-time payment to purchase credits',
        popular: false,
        features: [
            { name: 'Buy prompt credits as you go', included: true },
            { name: 'Scale usage with your team', included: true },
            { name: 'No hidden tiers, no locked features', included: true },
            { name: 'Credits never expire', included: true },
        ],
        cta: (
            <PurchaseModal isFromPricingPage>
                <Button variant="outline" className="mx-auto w-[282px] lg:w-[90%] py-5 rounded-md text-[14px] font-normal">
                    Buy Credits
                </Button>
            </PurchaseModal>
        ),
    },
    // {
    //     id: 'enterprise',
    //     name: 'Enterprise',
    //     icon: Building2,
    //     target: 'For Organizations',
    //     price: 'Custom',
    //     description: 'Tailored solutions for large-scale businesses',
    //     popular: false,
    //     features: [
    //         { name: 'Enterprise support', included: true },
    //         { name: 'Task Management', included: true },
    //         { name: 'Secure File Sharing (up to 5GB)', included: true },
    //         { name: 'Built-In Chat', included: true },
    //     ],
    // },
]

export default function PricingSection() {
    return (
        <div className="pt-8 pb-8 lg:pb-4 lg:pt-16 px-4 mb-0 md:mb-16 relative z-2 w-full mx-auto">
            <div className="w-full max-w-5xl mx-auto">
                {/* Pricing Grid */}
                <div className="grid grid-cols-1 md:grid-cols-[49%_50%] lg:grid-cols-[39%_60%] gap-6 lg:gap-2 xl:gap-4 w-full mx-auto">
                    {pricingPlans.map((plan) => {
                        const IconComponent = plan.icon
                        return (
                            <Card
                                key={plan.id}
                                className={` bg-white border border-[#0000001A] shadow-none rounded-[20px] overflow-hidden px-2`}>
                                <CardHeader className="p-2 pb-0">
                                    <div className="flex justify-between items-center mb-2">
                                        {/* Plan name */}
                                        <p className="text-xl font-medium text-gray-900 ">{plan.name}</p>
                                        {/* Target audience */}
                                        <div className="flex items-center gap-2 border border-[#0000001A] px-1 py-2 rounded-[6px] bg-[#00000005]">
                                            <IconComponent className="w-4 h-4" color="#000000B2" />
                                            <span className="text-xs text-gray-500">{plan.target}</span>
                                        </div>
                                    </div>

                                    {/* Description */}
                                    <p className="text-[#00000080] text-sm lg:text-[16px] mb-8">{plan.description}</p>
                                </CardHeader>

                                <CardContent
                                    className={`-mt-[40px] px-8 pt-4 ${plan.id === 'free' ? 'h-[450px]' : 'h-[448px]'} border rounded-[10px] bg-white relative`}>
                                    {/* Price */}
                                    <div className="mb-2 flex items-center gap-2">
                                        <div className="flex items-baseline gap-2">
                                            <h3 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-black">
                                                {typeof plan.price === 'string' ? plan.price : plan.price + '$'}
                                            </h3>
                                            {typeof plan.price !== 'string' && (
                                                <span className="text-[#00000080] text-lg ml-1">per user / month</span>
                                            )}
                                        </div>

                                        {plan.popular && (
                                            <div>
                                                <Badge className="bg-[#08B38B]  text-white text-xs px-3 py-1 font-medium rounded-full">
                                                    Most Popular
                                                </Badge>
                                            </div>
                                        )}
                                    </div>
                                    <p className="text-xs text-[#00000080]">{plan.text}</p>
                                    <div className="space-y-2 border-t pt-6 mt-3 mb-5 pb-3">
                                        {plan.features.map((feature, index) => (
                                            <div key={index} className="flex items-center gap-3">
                                                <div
                                                    className={`w-[15px] h-[15px] rounded-full flex items-center justify-center flex-shrink-0 ${
                                                        feature.included ? 'bg-[#08B38B]' : 'bg-[#0000001A]'
                                                    }`}>
                                                    {feature.included ? (
                                                        <Check className=" text-black" size={8} />
                                                    ) : (
                                                        <X className="" size={10} color="#0000001A" />
                                                    )}
                                                </div>
                                                <span
                                                    className={`text-sm ${feature.included ? 'text-gray-900' : 'text-gray-400'}`}>
                                                    {feature.name}
                                                </span>
                                            </div>
                                        ))}
                                    </div>

                                    {/* CTA Button */}

                                    <div className="absolute bottom-2 left-0 right-0 flex justify-center">{plan.cta}</div>
                                </CardContent>
                            </Card>
                        )
                    })}
                </div>
            </div>
        </div>
    )
}
