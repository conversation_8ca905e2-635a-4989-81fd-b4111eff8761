'use client'

import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { format } from 'date-fns'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useAuthStore } from '@/store/auth.store'
import { DataTable } from '@/components/custom-data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { useRouter } from 'next/navigation'
import { CirclePlus, Loader, Search, Settings2 } from 'lucide-react'
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { TeamInviteModal } from '@/components/team-invite-modal'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { Input } from '@/components/ui/input'
import { AccessModifier } from './team-management-components/access-modifier'

interface Team {
    id: string
    first_name: string
    last_name: string
    img_url?: string
    email: string
    workspace_role: {
        id: number
        label: string
    }
    created_at: string
}

const Page = () => {
    const [currentPage, setCurrentPage] = useState(1)
    const router = useRouter()
    const { setBreadcrumbs } = useBreadcrumbStore()
    const [searchQuery, setSearchQuery] = useState('')
    const [inviteModalOpen, setInviteModalOpen] = useState(false)
    const [inviteLink, setInviteLink] = useState('')
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)
    const workspaceId = currentWorkspace?.id
    const isAdmin = currentWorkspace?.is_admin
    const [isEmailInvite, setIsEmailSubmit] = useState(false)
    const [searchInput, setSearchInput] = useState('')

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'User Management', href: '/app/team-management' },
        ])
    }, [setBreadcrumbs])

    useEffect(() => {
        if (inviteModalOpen) {
            handleInvite('copy')
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [inviteModalOpen])

    useEffect(() => {
        const timer = setTimeout(() => {
            setSearchQuery(searchInput)
        }, 500)
        return () => clearTimeout(timer)
    }, [searchInput])

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchInput(e.target.value)
    }

    const columns: ColumnDef<Team>[] = useMemo(
        () => [
            {
                id: 'select',
                header: ({ table }) => (
                    <Checkbox
                        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                        aria-label="Select all"
                    />
                ),
                cell: ({ row }) => (
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => row.toggleSelected(!!value)}
                        aria-label="Select row"
                        id="select"
                    />
                ),
                enableSorting: false,
                enableHiding: false,
            },
            {
                accessorKey: 'name',
                header: 'User Name',
                cell: ({ row }) => {
                    return (
                        <div className="flex items-center gap-2">
                            <AssigneeAvatar
                                assignee={row.original.first_name + ' ' + row.original.last_name}
                                imageUrl={row.original.img_url}
                                className="border-2 border-white h-[24px] w-[24px]"
                            />
                            <span className="text-sm text-[#535862]">
                                {row.original.first_name + ' ' + row.original.last_name}
                            </span>
                        </div>
                    )
                },
            },
            {
                accessorKey: 'role',
                header: 'Role',
                cell: (
                    {
                        // row
                    },
                ) => {
                    return <div className="flex justify-center items-center text-[#535862] gap-2 w-fit">Software Tester</div>
                },
            },
            {
                accessorKey: 'email',
                header: 'Email Address',
                cell: ({ row }) => {
                    const email = row.original.email
                    return <span className="px-2 py-1 rounded text-sm text-[#535862] w-fit">{email}</span>
                },
            },

            {
                accessorKey: 'created_at',
                header: 'Date Added',
                cell: ({ row }) => {
                    const createdAt = row.original.created_at ?? ''
                    const formattedDate = createdAt ? format(new Date(createdAt), 'dd-MM-yyyy') : null

                    return <span className="text-sm text-[#535862]">{formattedDate}</span>
                },
            },
            {
                accessorKey: 'userAccess',
                header: 'Access',
                cell: ({ row }) => {
                    const userAccess = row.original.workspace_role
                    return <AccessModifier selectedRoles={[userAccess]} userId={row.original.id} />
                },
            },
        ],
        [],
    )

    const fetchUsers = useCallback(async () => {
        if (!workspaceId)
            return {
                data: [],
                paginationData: {
                    total: 0,
                    currentPage: 1,
                    totalPages: 1,
                    pageSize: 10,
                },
            }

        try {
            const response = await api.get(endpoints.user.userByWorkspace, {
                params: {
                    workspace_id: workspaceId,
                    searchQuery: searchQuery,
                    page: currentPage,
                    limit: 10,
                },
            })
            return response.data
        } catch (error) {
            toast.error('Error fetching users. Please try again later.')
            throw error
        }
    }, [workspaceId, searchQuery, currentPage])

    const { data, isLoading } = useQuery({
        queryKey: ['users', searchQuery, workspaceId, currentPage],
        queryFn: fetchUsers,
        enabled: !!workspaceId,
    })

    const userData = data?.data || []
    const paginationDetails = data?.pagination || {
        total: 0,
        currentPage: 1,
        totalPages: 1,
        pageSize: 10,
    }

    const handleNextPage = () => {
        if (currentPage < paginationDetails.totalPages) {
            setCurrentPage((prev) => prev + 1)
        }
    }

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage((prev) => prev - 1)
        }
    }

    const inviteMutation = useMutation({
        mutationFn: async (data: { emails?: string[]; link?: string; action: string }) => {
            const url = endpoints.workspace.invite.replace(':id', workspaceId?.toString() || '')
            const response = await api.post(url, data)
            return response.data
        },
        onSuccess: (data) => {
            if (isEmailInvite) {
                toast.success(data.message || 'Invitation has been sent via email.')
                setInviteModalOpen(false)
                setIsEmailSubmit(false)
                return
            }
            setInviteLink(data?.invite_link)
        },
        onError: (error: AxiosError) => {
            const errorText = (error.response?.data as { error?: string })?.error
            axiosErrorToast(error, errorText || 'Failed to send invite. Please try again.')
        },
    })

    const handleInvite = async (action: string, emails?: string[], link?: string) => {
        await inviteMutation.mutate({ emails, link, action })
    }

    const handleInviteWithEmail = (emails: string[]) => {
        handleInvite('email', emails)
        setIsEmailSubmit(true)
    }

    const handleAddTeamMember = () => {
        if (!isAdmin) {
            toast.error('Only admins can add team members')
            return
        }
        setInviteModalOpen(true)
    }

    if (!workspaceId || isLoading) {
        return (
            <div className="p-4 w-full flex items-center justify-center h-[40vh]">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <>
            <div className="space-y-4 mb-2">
                <div>
                    <p className="text-2xl font-semibold">User Management</p>
                    <p className="text-xs text-[#71717A]">Showing {userData.length} Team Members</p>
                </div>
                <div className="flex gap-2">
                    <div className="relative w-full bg-white">
                        <Search className="absolute left-2.5 top-3 h-4 w-4 text-gray-500" />
                        <Input
                            type="text"
                            placeholder="Search ..."
                            className="pl-8 h-10 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-slate-200"
                            value={searchInput}
                            onChange={handleSearch}
                        />
                    </div>
                    <Button className="rounded-[6px] h-[40px]" variant="outline">
                        <Settings2 className=" h-4 w-4" />
                        Filter
                    </Button>
                    <Button className="rounded-[6px] h-[40px]" onClick={handleAddTeamMember}>
                        <CirclePlus className=" h-4 w-4" />
                        Team Member
                    </Button>
                </div>
            </div>
            {userData.length === 0 ? (
                <div>There is no team members to show</div>
            ) : (
                <DataTable
                    columns={columns}
                    data={userData}
                    handleNextPage={handleNextPage}
                    handlePreviousPage={handlePreviousPage}
                    paginationDetails={paginationDetails}
                    onRowClick={(row) => {
                        router.push(`users/${row.id}`) // Your dynamic route
                    }}
                />
            )}

            <TeamInviteModal
                showTrigger={false}
                isOpen={inviteModalOpen}
                onOpenChange={() => {
                    setInviteModalOpen(false)
                }}
                defaultLink={inviteLink}
                onInvite={(emails) => handleInviteWithEmail(emails)}
                isLoading={inviteMutation.isPending}
            />
        </>
    )
}
export default Page
