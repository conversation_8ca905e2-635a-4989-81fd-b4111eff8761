import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

interface User {
    id: number
    email: string
    first_name: string
    last_name: string
    img_url: string
    cover_image_url: string
    user_type: number
    team_member_role: {
        id: number
        role: string
    }
}

interface UserProfileHeaderProps {
    user: User
}

const UserProfileHeader = ({ user }: UserProfileHeaderProps) => {
    return (
        <div>
            <div
                style={{
                    backgroundImage: user?.cover_image_url ? `url(${user?.cover_image_url})` : undefined,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                }}
                className="relative shadow-xs flex w-full h-[228px] justify-end bg-gradient-to-r from-[#1F4037] to-[#99F2C8A6] rounded-[15px]">
                <div className="flex  absolute -bottom-25 left-8">
                    <div>
                        <AssigneeAvatar
                            assignee={user?.first_name + ' ' + user?.last_name}
                            imageUrl={user?.img_url}
                            className="border-2 border-white h-[161px] w-[161px]"
                            fallbackClassName="text-2xl"
                        />
                    </div>
                    <div className="flex flex-col justify-end pl-4 pb-6">
                        <p className="text-[25px] text-[#1E293B] font-medium">{user?.first_name + ' ' + user?.last_name}</p>
                        <p className="text-sm text-[#475569]">{user?.team_member_role?.role}</p>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default UserProfileHeader
