import { ArrowBigUp, MessageSquare } from 'lucide-react'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { CommentType } from '.'
import CommentActions from './comment-action'
import EditComment from './edit-comment'
import { useAuthStore } from '@/store/auth.store'
import Markdown from 'react-markdown'
import { cn } from '@/lib/utils'

const CommentItem = ({
    comment,
    onUpvote,
    onReply,
    isReply = false,
    setEditingItem,
    isEditing,
    onAfterDeleteSuccess,
    editComment,
}: {
    comment: CommentType
    onUpvote: (commentId: number) => void
    onReply: () => void
    isReply?: boolean
    setEditingItem: (state: CommentType | null) => void
    isEditing: boolean
    onAfterDeleteSuccess: () => void
    editComment: (data: { id: number; content: string }) => void
}) => {
    const { user } = useAuthStore()
    const userName = `${comment?.creator?.first_name} ${comment?.creator?.last_name}`
    const upvotedUserIds = comment?.upvotes.flatMap((upvote: { user_id: number }) => upvote.user_id) || []
    const isUpvoted = user?.id && upvotedUserIds?.includes(user?.id)

    return (
        <div className="flex gap-2 py-2 px-2 border-b mb-1 w-full">
            <AssigneeAvatar
                assignee={userName}
                imageUrl={comment?.creator.img_url || ''}
                className="border-2 border-white h-[42px] w-[42px]"
            />
            <div className="w-full">
                <div className="flex justify-between items-center">
                    <div className="flex gap-2">
                        <div className="text-sm text-gray-600">
                            <div className="text-sm font-medium text-gray-900">
                                <p>
                                    <strong>{userName}</strong>
                                    <span className="text-xs text-[#1E1E1E99] ml-2">commented</span>
                                </p>
                                <span className="text-xs text-[#1E1E1E99]">{comment?.time_ago}</span>
                                <span className="text-xs text-[#1E1E1E99]">
                                    {' '}
                                    {new Date(comment?.updated_at) > new Date(comment?.created_at) ? '(edited)' : ''}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex-1 space-y-2">
                    {!isEditing ? (
                        <div className="text-sm text-[#1E1E1E] my-2">
                            <Markdown
                                components={{
                                    ul: ({ children }) => (
                                        <ul className="list-disc list-inside ml-4 space-y-1 my-2">{children}</ul>
                                    ),
                                    ol: ({ children }) => (
                                        <ol className="list-decimal list-inside ml-4 space-y-1 my-2">{children}</ol>
                                    ),
                                    li: ({ children }) => <li className="text-sm leading-relaxed">{children}</li>,
                                    a: ({ children, href }) => (
                                        <a
                                            href={href}
                                            className="text-blue-500 hover:text-blue-700 hover:underline"
                                            target="_blank"
                                            rel="noopener noreferrer">
                                            {children}
                                        </a>
                                    ),
                                }}>
                                {comment?.content}
                            </Markdown>
                        </div>
                    ) : (
                        <div className="text-sm text-[#1E1E1E] my-2 ml-6">
                            <p
                                className="text-xs text-end text-[#1E1E1E99] cursor-pointer pr-4 pb-2"
                                onClick={() => setEditingItem(null)}>
                                cancel
                            </p>
                            <EditComment comment={comment} editComment={editComment} />
                        </div>
                    )}

                    <div className={cn('flex items-center gap-2 text-gray-500 text-xs', comment?.upvotes.length > 0 && 'gap-12')}>
                        <p>
                            {comment?.upvotes.length > 0
                                ? isUpvoted
                                    ? comment.upvotes.length > 1
                                        ? `You & ${comment.upvotes.length - 1} others`
                                        : 'You upvoted'
                                    : `${comment.upvotes.length} upvote${comment.upvotes.length > 1 ? 's' : ''}`
                                : ''}
                        </p>
                        <p>{comment?.replies?.length || 0} replies</p>
                    </div>

                    <div className="flex items-center justify-center gap-4 rounded-[6px] bg-white text-sm border p-2 w-fit max-w-[25%]">
                        <button
                            onClick={() => onUpvote(comment?.id)}
                            className="flex cursor-pointer items-center text-gray-500 hover:text-green-600 transition-colors">
                            <ArrowBigUp
                                size={18}
                                fill={isUpvoted ? '#2FA87A' : 'transparent'}
                                color={isUpvoted ? '#2FA87A' : '#000'}
                                strokeWidth={1}
                            />
                        </button>

                        {!isReply && onReply && (
                            <button
                                className="flex cursor-pointer items-center gap-1 text-gray-500 hover:text-gray-700 transition-colors"
                                onClick={onReply}>
                                <MessageSquare size={14} />
                                {/* <span className="text-xs">Reply</span> */}
                            </button>
                        )}
                        <div className="flex items-center justify-center text-gray-500 text-xs">
                            <CommentActions
                                comments={comment}
                                onAftedDeleteSuccess={onAfterDeleteSuccess}
                                setEditingItem={setEditingItem}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CommentItem
