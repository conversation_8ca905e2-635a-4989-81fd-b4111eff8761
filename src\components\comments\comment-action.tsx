'use client' // If using app directory

import { useState } from 'react'
import { MoreHorizontal, MoreVertical } from 'lucide-react' // Optional icon
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import endpoints from '@/services/api-endpoints'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Dialog } from '@/components/ui/dialog'
import { CommentType } from '.'
import { useAuthStore } from '@/store/auth.store'

interface CommentActionsDropdownProps {
    comments: CommentType
    onAftedDeleteSuccess: () => void
    // setIsEditing: (state: boolean) => void
    setEditingItem: (state: CommentType | null) => void
    isReply?: boolean
}

export default function CommentActions({ comments, onAftedDeleteSuccess, setEditingItem, isReply }: CommentActionsDropdownProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false)
    const [deleteOpen, setDeleteOpen] = useState(false)
    const url = `${endpoints.comments.deleteComment.replace(':commentId', comments?.id.toString())}`
    const { user } = useAuthStore()
    const loginedUserId = user?.id
    const isCommentOwner = loginedUserId === comments?.created_by

    const handleEdit = () => {
        setIsMenuOpen(false) // Close dropdown first
        setTimeout(() => setEditingItem(comments), 100) // Small delay to avoid conflicts
    }

    const handleDelete = () => {
        setIsMenuOpen(false) // Close dropdown first
        setTimeout(() => setDeleteOpen(true), 100) // Small delay to avoid conflicts
    }

    return (
        <div>
            {isCommentOwner && (
                <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-fit w-fit">
                            {isReply ? <MoreHorizontal size={18} /> : <MoreVertical size={18} />}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleEdit}>Edit</DropdownMenuItem>
                        <DropdownMenuItem onClick={handleDelete}>Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            )}

            <Dialog open={deleteOpen} onOpenChange={setDeleteOpen}>
                <DeleteWithAlert
                    title="Are you sure you want to delete ?"
                    description="This action cannot be undone."
                    endpoint={url}
                    onAfterSuccess={onAftedDeleteSuccess}
                    isAlertOpen={deleteOpen}
                    setIsAlertOpen={setDeleteOpen}
                />
            </Dialog>
        </div>
    )
}
