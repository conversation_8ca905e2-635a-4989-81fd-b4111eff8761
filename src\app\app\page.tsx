'use client'
import { useState, useEffect, useRef, type ChangeEvent, type KeyboardEvent, useCallback } from 'react'
import type React from 'react'
import { motion } from 'framer-motion'
import {
    ZapIcon,
    Loader2Icon,
    RefreshCcw,
    Brain,
    Paperclip,
    List,
    Rocket,
    Maximize,
    Minimize,
    GalleryVerticalEnd,
} from 'lucide-react'
import Image from 'next/image'

// UI Components
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'

// Custom Components
import { LoadingDots, MessageBubble } from '@/components/chat/message-bubble'
import TaskCard from '@/components/scratchpad-task-card'
import { ProjectSelector } from '@/components/project-selector'
import { HistoryTab } from '@/components/chat/history-tab'

// Hooks and Stores
import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { useAuthStore } from '@/store/auth.store'
import { useAIAgent } from '@/hooks/use-ai-agent'

// Constants and Types
import { CHAT_SUGGESTIONS } from '@/constants/chat-suggestions'
import type { Message } from '@/types/project'

// Assets
import sendMessageIcon from '../../../public/assets/icons/send-message.svg'

// Utils
import { toast } from 'sonner'
import { useTabStore } from '@/store/tabs.store'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useProjectStore } from '@/store/project.store'
import { useRouter, useSearchParams } from 'next/navigation'
import { useGlobalState } from '@/store/global-states.store'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'

const tabItems = [
    {
        value: 'chat',
        label: 'Chat',
        activeIcon: <Image alt="" src="/assets/icons/chat-active.svg" width={16} height={16} />,
        inactiveIcon: <Image alt="" src="/assets/icons/chat-inactive.svg" width={16} height={16} />,
    },
    {
        value: 'history',
        label: 'History',
        activeIcon: <GalleryVerticalEnd fill="#2A2A2A" />,
        inactiveIcon: <GalleryVerticalEnd color="#B5B5B5" fill="#B5B5B5" />,
    },
]

const GREETINGS = [
    "What's new, {name}?",
    'Hello, {name}!',
    'Hey there, {name}!',
    'Good to see you, {name}!',
    'Welcome back, {name}!',
    "How's it going, {name}?",
    'Lets build something amazing.',
    "Let's get started, {name}!",
    'What can we create today?',
    'Time to make magic happen.',
]

const getRandomGreeting = (name: string | undefined) => {
    const randomIndex = Math.floor(Math.random() * GREETINGS.length)
    return GREETINGS[randomIndex].replace('{name}', name || '')
}

const ProjectCreationAgent = () => {
    // ==================== HOOKS AND STORES ====================
    const { setBreadcrumbs } = useBreadcrumbStore()
    const { fetchMyDetails, currentWorkspace, getUserId } = useAuthStore()
    const abortControllerRef = useRef<AbortController | null>(null)
    const { setMessages, setTasks, setShowTasksPanel } = useProjectStore()
    const {
        messages,
        tasks,
        selectedProject,
        showTasksPanel,
        isLoading,
        isSaving,
        sendMessage,
        generateTasks,
        publishTasks,
        resetProject,
        setProjectSession,
        hasMessages,
        abortCurrentRequest,
    } = useAIAgent({ abortControllerRef })

    // ==================== LOCAL STATE ====================
    const [expandtaskPanel, setExpandTaskPanel] = useState(false)
    const [inputValue, setInputValue] = useState('')
    const { activeTab, setActiveTab } = useTabStore()
    const { invitationUrl, oAuthLogin } = useAuthStore()
    const params = useSearchParams()
    const router = useRouter()
    const { setActiveSession, activeSession } = useGlobalState()
    const { user } = useAuthStore()
    const [greeting, setGreeting] = useState('')

    // Enhanced scroll state with user interaction tracking
    const [chatShouldAutoScroll, setChatShouldAutoScroll] = useState(true)
    const [tasksShouldAutoScroll, setTasksShouldAutoScroll] = useState(true)
    const [userScrolledDuringStream, setUserScrolledDuringStream] = useState(false)
    const [userScrolledTasksDuringStream, setUserScrolledTasksDuringStream] = useState(false)

    // ==================== REFS ====================
    const chatContainerRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>
    const tasksContainerRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const chatScrollTimeoutRef = useRef<NodeJS.Timeout>(null)
    const tasksScrollTimeoutRef = useRef<NodeJS.Timeout>(null)

    // ==================== COMPUTED VALUES ====================
    const SUGGESTIONS_TEMPLATES = CHAT_SUGGESTIONS
    const accessToken = params.get('accessToken')

    // ==================== SCROLL UTILITIES ====================
    const getScrollElement = (containerRef: React.RefObject<HTMLDivElement> | null) => {
        return containerRef?.current?.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement | null
    }

    const isAtBottom = (element: HTMLElement, threshold = 50) => {
        return element.scrollHeight - element.scrollTop - element.clientHeight <= threshold
    }

    const scrollToBottom = (element: HTMLElement, smooth = false) => {
        if (smooth) {
            element.scrollTo({ top: element.scrollHeight, behavior: 'smooth' })
        } else {
            element.scrollTop = element.scrollHeight
        }
    }

    // ==================== ENHANCED SCROLL HANDLERS ====================
    const handleChatScroll = useCallback(() => {
        const scrollElement = getScrollElement(chatContainerRef)
        if (!scrollElement) return

        // Clear existing timeout
        if (chatScrollTimeoutRef.current) {
            clearTimeout(chatScrollTimeoutRef.current)
        }

        // Debounce scroll handling
        chatScrollTimeoutRef.current = setTimeout(() => {
            const atBottom = isAtBottom(scrollElement)

            if (atBottom) {
                // User is at bottom - enable auto scroll and clear stream scroll flag
                if (!chatShouldAutoScroll) {
                    setChatShouldAutoScroll(true)
                }
                if (userScrolledDuringStream) {
                    setUserScrolledDuringStream(false)
                }
            } else if (isLoading && chatShouldAutoScroll) {
                // During streaming, if we're not at bottom but auto-scroll is enabled,
                // this means user manually scrolled up - disable auto scroll
                setChatShouldAutoScroll(false)
                setUserScrolledDuringStream(true)
            } else if (!isLoading && chatShouldAutoScroll) {
                // Not loading and not at bottom - user scrolled up
                setChatShouldAutoScroll(false)
            }
        }, 100)
    }, [chatShouldAutoScroll, isLoading, userScrolledDuringStream])

    const handleTasksScroll = useCallback(() => {
        const scrollElement = getScrollElement(tasksContainerRef)
        if (!scrollElement) return

        // Clear existing timeout
        if (tasksScrollTimeoutRef.current) {
            clearTimeout(tasksScrollTimeoutRef.current)
        }

        // Debounce scroll handling
        tasksScrollTimeoutRef.current = setTimeout(() => {
            const atBottom = isAtBottom(scrollElement)

            if (atBottom) {
                // User is at bottom - enable auto scroll and clear stream scroll flag
                if (!tasksShouldAutoScroll) {
                    setTasksShouldAutoScroll(true)
                }
                if (userScrolledTasksDuringStream) {
                    setUserScrolledTasksDuringStream(false)
                }
            } else if (isLoading && tasksShouldAutoScroll) {
                // During streaming, if we're not at bottom but auto-scroll is enabled,
                // this means user manually scrolled up - disable auto scroll
                setTasksShouldAutoScroll(false)
                setUserScrolledTasksDuringStream(true)
            } else if (!isLoading && tasksShouldAutoScroll) {
                // Not loading and not at bottom - user scrolled up
                setTasksShouldAutoScroll(false)
            }
        }, 100)
    }, [tasksShouldAutoScroll, isLoading, userScrolledTasksDuringStream])

    // ==================== ENHANCED AUTO SCROLL EFFECTS ====================
    // Chat auto scroll effect with improved streaming logic
    useEffect(() => {
        const scrollElement = getScrollElement(chatContainerRef)
        if (!scrollElement) return

        // Add scroll listener
        scrollElement.addEventListener('scroll', handleChatScroll, { passive: true })

        if (isLoading && chatShouldAutoScroll && !userScrolledDuringStream) {
            requestAnimationFrame(() => {
                scrollToBottom(scrollElement)
            })
        }

        return () => {
            scrollElement.removeEventListener('scroll', handleChatScroll)
        }
    }, [messages, chatShouldAutoScroll, isLoading, handleChatScroll, userScrolledDuringStream])

    // Tasks auto scroll effect with improved streaming logic
    useEffect(() => {
        const scrollElement = getScrollElement(tasksContainerRef)
        if (!scrollElement) return

        // Add scroll listener
        scrollElement.addEventListener('scroll', handleTasksScroll, { passive: true })

        // Enhanced auto scroll logic
        const shouldAutoScroll = tasksShouldAutoScroll && !userScrolledTasksDuringStream

        if (shouldAutoScroll || (isLoading && tasksShouldAutoScroll && !userScrolledTasksDuringStream)) {
            requestAnimationFrame(() => {
                scrollToBottom(scrollElement)
            })
        }

        return () => {
            scrollElement.removeEventListener('scroll', handleTasksScroll)
        }
    }, [tasks, tasksShouldAutoScroll, isLoading, handleTasksScroll, userScrolledTasksDuringStream])

    // ==================== OTHER EFFECTS ====================
    // Initialize breadcrumbs and user details
    useEffect(() => {
        if (invitationUrl) router.push(invitationUrl)
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Chat', href: '/app' },
        ])
        if (accessToken) {
            oAuthLogin(accessToken)
            router.replace('/app')
        }
        fetchMyDetails()
    }, [setBreadcrumbs, fetchMyDetails, oAuthLogin, accessToken, invitationUrl, router])

    useEffect(() => {
        return () => {
            abortCurrentRequest()
            if (chatScrollTimeoutRef.current) {
                clearTimeout(chatScrollTimeoutRef.current)
            }
            if (tasksScrollTimeoutRef.current) {
                clearTimeout(tasksScrollTimeoutRef.current)
            }
        }
    }, [abortCurrentRequest])

    // Reset project when workspace changes
    useEffect(() => {
        if (currentWorkspace?.id) {
            resetProject()
            abortCurrentRequest()
            setActiveSession(null)
            // Reset all scroll states
            setChatShouldAutoScroll(true)
            setTasksShouldAutoScroll(true)
            setUserScrolledDuringStream(false)
            setUserScrolledTasksDuringStream(false)
        }
    }, [currentWorkspace?.id, abortCurrentRequest, resetProject, setActiveSession])

    useEffect(() => {
        if (activeSession) {
            setShowTasksPanel(false)
            setTasks([])
        }
    }, [activeSession, setShowTasksPanel, setTasks])

    const { data: userCredits, refetch: refetchUserCredits } = useQuery({
        queryKey: ['user'],
        queryFn: async () => {
            try {
                const response = await api.get(endpoints.llm.getCredits)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch user details')
                throw error
            }
        },
        // enabled: !!accessToken,
    })

    // Add this useEffect to set greeting only on client
    useEffect(() => {
        setGreeting(getRandomGreeting(user?.first_name))
    }, [user?.first_name])

    // ==================== ENHANCED EVENT HANDLERS ====================
    const handleSendMessage = async () => {
        if (!inputValue.trim()) return
        if (!selectedProject) {
            toast.error('Please select a project before starting a conversation.')
            return
        }

        // Reset scroll states for new message
        setChatShouldAutoScroll(true)
        setUserScrolledDuringStream(false)

        // Immediately scroll to bottom
        const scrollElement = getScrollElement(chatContainerRef)
        if (scrollElement) {
            scrollToBottom(scrollElement, true)
        }

        await sendMessage(inputValue)
        refetchUserCredits()
        setInputValue('')
        setFocusForTextarea()
    }

    const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSendMessage()
        }
    }

    const handleTemplateClick = (template: string) => {
        setInputValue(template)
        setFocusForTextarea()
    }

    const handleGenerateTasks = async () => {
        // Reset scroll states for new task generation
        setTasksShouldAutoScroll(true)
        setUserScrolledTasksDuringStream(false)

        // Immediately scroll to bottom
        const scrollElement = getScrollElement(tasksContainerRef)
        if (scrollElement) {
            scrollToBottom(scrollElement, true)
        }

        await generateTasks()
        refetchUserCredits()
        setFocusForTextarea()
    }

    const handleSelectSession = async (sessionId: string, messages: Message[]) => {
        setActiveTab('chat')
        setProjectSession(sessionId)
        setMessages(messages)

        // Reset all scroll states when selecting new session
        setChatShouldAutoScroll(true)
        setTasksShouldAutoScroll(true)
        setUserScrolledDuringStream(false)
        setUserScrolledTasksDuringStream(false)

        // Scroll to bottom after messages are set
        setTimeout(() => {
            const scrollElement = getScrollElement(chatContainerRef)
            if (scrollElement) {
                scrollToBottom(scrollElement, false)
            }
        }, 100)
    }

    const handlePublishTasks = async () => {
        await publishTasks()
        setExpandTaskPanel(false)
    }

    const handleTabChange = (tab: string) => {
        setActiveTab(tab)
    }

    // ==================== UTILITY FUNCTIONS ====================
    const setFocusForTextarea = () => {
        setTimeout(() => {
            textareaRef.current?.focus()
        }, 100)
    }

    // ==================== RENDER HELPERS ====================
    const renderWelcomeSection = () => (
        <>
            {/* <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-center text-black dark:text-white mb-2">
                <Badge variant="outline" className="text-sm py-2 px-4">
                    <span className="inline-flex items-center gap-1 text-[#4DB068] font-medium">
                        Upgrade <Wand size={15} />
                    </span>
                    <span className="text-[#9C9C9C] font-normal">free plan to full version</span>
                </Badge>
            </motion.h2> */}
            <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="font-serif text-center text-[#3C3C3C] font-normal text-[45px] dark:text-white mb-2">
                {greeting || `Hello, ${user?.first_name || 'there'}!`}
            </motion.h2>
            <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-medium mb-4 flex flex-row">
                <p className="w-full text-sm font-medium text-[#666F8D] text-center">
                    Raydian is your AI agent for projects, tasks, and team insights.
                </p>
            </motion.h1>
        </>
    )

    const renderChatMessages = () => (
        <ScrollArea
            ref={chatContainerRef}
            className={` max-w-full pr-4 space-y-2 flex flex-col ${showTasksPanel ? 'h-[62vh] mb-2' : 'h-[72vh] mt-7'}`}>
            <>
                {messages.map((message) => (
                    <MessageBubble
                        key={message.id}
                        message={message}
                        refetchUserCredits={refetchUserCredits}
                        generateTasks={handleGenerateTasks}
                    />
                ))}
            </>
        </ScrollArea>
    )

    const renderInputSection = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className={` flex flex-col  border rounded-[16px] h-fit border-[#59BC611A] shadow-md bg-white ${
                hasMessages
                    ? showTasksPanel
                        ? 'w-full'
                        : 'w-[56%] mx-auto fixed bottom-2 h-[115px]'
                    : 'bg-white h-[180px] justify-between mt-2'
            }`}>
            {/* Input Area */}
            <div className={`mb-2 px-3 py-2  ${hasMessages ? 'h-[80px]' : 'max-h-[60px]'}`}>
                <Textarea
                    ref={textareaRef}
                    className="w-full pl-2 text-sm max-h-[10px] text-gray-600 resize-none dark:text-gray-300 bg-transparent border-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    placeholder={selectedProject ? 'How can I help you with your project?' : 'How can I help you?'}
                    value={inputValue}
                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    disabled={isLoading}
                    name="message"
                />
            </div>
            {/* Bottom Controls */}
            <div className="flex items-center justify-between px-3 py-2 bg-[#fff] rounded-b-[16px]">
                <div className="flex items-center gap-2">
                    <div className="flex space-x-4">
                        <div className="relative">
                            <ProjectSelector
                                onProjectSelect={() => setFocusForTextarea()}
                                onCreateNew={() => setFocusForTextarea()}
                                className="w-auto"
                                disableSelect={hasMessages}
                            />
                        </div>
                    </div>
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.8 }}
                        className="flex items-center text-xs text-[#9C9C9C]">
                        <ZapIcon className="h-4 w-4 mr-2" />
                        <span>
                            {userCredits?.creditsRemaining}/{userCredits?.totalCredit} Prompts
                        </span>
                    </motion.div>
                </div>
                <div className="flex items-center rounded-[11px] bg-[#1D1D1D0A] pl-4 pt-1 max-h-[40px]">
                    <Brain className="h-4 w-4 text-[#6C7491] cursor-pointer mr-2" />
                    <Paperclip className="h-4 w-4 text-[#6C7491] cursor-pointer" />
                    <Button
                        size="icon"
                        variant="ghost"
                        onClick={handleSendMessage}
                        className="relative w-[64px] p-0 hover:bg-transparent -mr-2"
                        disabled={isLoading || !inputValue.trim()}>
                        <Image
                            src={sendMessageIcon || '/placeholder.svg'}
                            alt="Send Message"
                            width={48}
                            height={34}
                            className="h-auto w-auto"
                        />
                    </Button>
                </div>
            </div>
        </motion.div>
    )

    const renderSuggestions = () => (
        <motion.div initial="hidden" animate="show" className="mt-2 flex flex-row gap-2">
            <motion.div className="flex flex-row w-full items-center gap-2 justify-around mt-2">
                {SUGGESTIONS_TEMPLATES.map((suggestion) => (
                    <motion.div
                        key={suggestion.id}
                        className="flex w-fit cursor-pointer bg-[#5BBF6005] rounded-[13px] px-3 py-3 text-xs text-[#666F8D] border border-[#43424217]"
                        onClick={() => handleTemplateClick(suggestion.content)}
                        whileTap={{ scale: 0.95 }}>
                        {suggestion.icon && <suggestion.icon className="h-4 w-4 mr-2" />}
                        <span className="text-[#666F8D] text-[12px] font-medium">{suggestion.title}</span>
                    </motion.div>
                ))}
            </motion.div>
        </motion.div>
    )

    const renderTasksPanel = () => (
        <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className={`${
                expandtaskPanel ? 'w-full' : 'w-1/2'
            } rounded-xl px-6 py-1 border border-[#0000000F] bg-white dark:bg-gray-900 flex flex-col mx-auto overflow-hidden max-h-[90vh]`}>
            {/* Tasks Panel Header */}
            <div className="flex justify-between items-center mb-2">
                <div className="flex gap-2 items-center text-[15px]">
                    <span>
                        <List size={15} color="#6B6B6B" />
                    </span>
                    <span className="text-[#6B6B6B]">Total Tasks ({tasks.length})</span>
                </div>
                <div className="flex items-center gap-4">
                    <div onClick={handleGenerateTasks} className="flex items-center gap-2 py-4 cursor-pointer">
                        <RefreshCcw className="h-[24px] w-[24px]" color="#60646C" strokeWidth={1} />
                    </div>
                    <Button
                        size="sm"
                        onClick={handlePublishTasks}
                        disabled={isSaving || tasks.length === 0}
                        className="flex items-center gap-2 py-4">
                        {isSaving ? <Loader2Icon className="h-4 w-4 mr-2 animate-spin" /> : <Rocket className="h-4 w-4 mr-2" />}
                        <span>{isSaving ? 'Saving...' : 'Publish'}</span>
                    </Button>
                    <div
                        onClick={() => setExpandTaskPanel(!expandtaskPanel)}
                        className="flex items-center gap-2 py-4 cursor-pointer">
                        <span>
                            {expandtaskPanel ? (
                                <Minimize className="h-[24px] w-[24px]" color="#60646C" strokeWidth={1} />
                            ) : (
                                <Maximize className="h-[24px] w-[24px]" color="#60646C" strokeWidth={1} />
                            )}
                        </span>
                    </div>
                </div>
            </div>
            {/* Tasks List */}
            <ScrollArea ref={tasksContainerRef} className="flex-grow overflow-y-auto pr-2">
                <>
                    {tasks.length === 0 && isLoading && (
                        <div className="h-80 flex justify-center items-center">
                            <LoadingDots />
                        </div>
                    )}
                    {tasks.map((task, index) => (
                        <TaskCard
                            key={task.id}
                            taskNum={index + 1}
                            id={task.id}
                            category={task.category}
                            title={task.title}
                            description={task.description}
                            assignedRole={task.assignedRole}
                            estimatedHours={task.estimatedHours}
                            dependencies={task.dependencies}
                            showBottomBorder={index !== tasks.length - 1}
                            acceptanceCriteria={task.acceptanceCriteria}
                        />
                    ))}
                </>
            </ScrollArea>
        </motion.div>
    )

    // ==================== MAIN RENDER ====================
    return (
        <div className={`flex h-[90vh] p-4 ${hasMessages ? '' : 'justify-center items-center '}`}>
            <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList className=" bg-transparent border absolute top-4 left-12 z-10 rounded-lg">
                    {tabItems.map((tab) => (
                        <TabsTrigger
                            key={tab.value}
                            value={tab.value}
                            className="data-[state=active]:bg-[#E5E7EB] data-[state=active]:text-[#2A2A2A] text-[#B5B5B5]  px-4 py-2 rounded-md">
                            {activeTab === tab.value ? tab.activeIcon : tab.inactiveIcon}
                            {tab.label}
                        </TabsTrigger>
                    ))}
                </TabsList>
            </Tabs>

            {/* History Panel */}
            {activeTab === 'history' && <HistoryTab userId={String(getUserId())} onSelectSession={handleSelectSession} />}

            {/* Main Chat Panel */}
            {activeTab === 'chat' && (
                <>
                    <div
                        className={`rounded-xl px-6 pb-4 flex flex-col transition-all duration-500 ease-in-out w-full max-w-2xl -mt-[60px]
                             ${
                                 !hasMessages
                                     ? 'w-full max-w-3xl mx-auto -mt-8'
                                     : showTasksPanel && selectedProject
                                       ? expandtaskPanel
                                           ? 'hidden'
                                           : 'w-1/2 pt-10'
                                       : 'max-w-[900px] mx-auto bg-none'
                             }`}>
                        <div className={`${expandtaskPanel ? 'hidden' : ' flex flex-col justify-center'}`}>
                            {/* Welcome Section */}
                            {!hasMessages && renderWelcomeSection()}
                            {/* Chat Messages */}
                            {hasMessages && renderChatMessages()}
                            {/* Input Section */}
                            {renderInputSection()}
                            {/* Suggestions */}
                            {!hasMessages && renderSuggestions()}
                        </div>
                    </div>
                    {/* Tasks Panel */}
                    {showTasksPanel && selectedProject && renderTasksPanel()}
                </>
            )}
        </div>
    )
}

export default ProjectCreationAgent
