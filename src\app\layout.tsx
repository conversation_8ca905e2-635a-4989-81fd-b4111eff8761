// app/layout.tsx
import type { Metadata } from 'next'
import './globals.css'
import { Toaster } from '@/components/ui/sonner'
import QueryProvider from '@/components/query-provider'
import { <PERSON>, Besley } from 'next/font/google'
import Analytics from '@/components/analytics-tracker'

export const metadata: Metadata = {
    title: {
        default: 'Raydian.ai',
        template: 'Raydian.ai - %s ',
    },
    description:
        'AI-powered project planning tool that transforms your ideas into structured workflows. Start conversations, generate plans instantly, and execute with perfect clarity.',
    keywords:
        'AI project management, task planning, workflow automation, project planning tool, AI co-pilot, AI assistant for project management, AI-powered project management tool',
    authors: [{ name: 'Raydian.ai' }],
    creator: 'Raydian.ai',
    metadataBase: new URL('https://raydian.ai'),
    alternates: {
        canonical: '/',
    },
    icons: {
        icon: '/favicon_io/favicon.ico',
    },
    openGraph: {
        title: 'Raydian.ai - Project Management Made Easy',
        description:
            'An AI-powered project planning tool that turns your ideas into structured workflows, helping you organize, collaborate, and achieve goals more efficiently.',
        url: 'https://raydian.ai',
        siteName: 'Raydian.ai',
        images: [
            {
                url: 'https://minio.raydian.ai/raydian-static/opengraph-image.png',
                width: 1200,
                height: 630,
                alt: 'Raydian AI project planning interface',
            },
        ],
        locale: 'en_US',
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Raydian.ai - Project Management Made Easy',
        description:
            'An AI-powered project planning tool that turns your ideas into structured workflows, helping you organize, collaborate, and achieve goals more efficiently.',
        images: ['https://minio.raydian.ai/raydian-static/opengraph-image.png'],
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
    category: 'technology',
    classification: 'Business Software',
    referrer: 'origin-when-cross-origin',
}

// Load fonts from Google with optimization
const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-inter',
})

const besley = Besley({
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-besley',
})

const googleAnalyticsId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en" className={`${inter.variable} ${besley.variable}`}>
            <body className="antialiased font-sans">
                {googleAnalyticsId && <Analytics measurementId={googleAnalyticsId} />}
                <QueryProvider>{children}</QueryProvider>
                <Toaster />
            </body>
        </html>
    )
}
