'use client'

import { ProjectProgress } from '@/components/project-progress'

interface Department {
    id: number
    short_code: string
    label: string
    colour: {
        text: string
        border: string
        background: string
    }
    icon: string
}

interface ProjectData {
    id: number
    name: string
    overview: string
    workspace_id: number
    status_id: number
    stack: Array<string>
    createdAt: string
    updatedAt: string
    deletedAt: null
    created_by: number
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    workspace: {
        id: number
        name: string
    }
    status: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    features: {
        completed: number
        total: number
    }
    departments: Department[]
    team: Array<{
        id: number
        first_name: string
        last_name: string
        avatar?: string
    }>
    estimated_effort: string
    timeline: {
        start_date: string
        end_date: string
        days: number
    }
    priority: {
        level: string
        color: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
        completedDays: number
        totalDays: number
        projectStatusText: string
    }
}

export function ProjectProgressDetails({ project }: { project: ProjectData }) {
    const projectDataForProgress = {
        title: project.name,
        progressPercentage: project.progress.percentage,
        currentDay: project.progress.completedDays,
        totalDays: project.progress.totalDays,
        isOnTrack: project.progress.projectStatusText === 'On Track',
        projectStatusText: project.progress.projectStatusText,
        metrics: {
            features: project.features?.total,
            tasks: project.progress.total, //total tasks
            teamMembers: project.team?.length,
        },
    }

    return (
        <>
            <div className="flex flex-col gap-2">
                <ProjectProgress {...projectDataForProgress} />
            </div>
        </>
    )
}
