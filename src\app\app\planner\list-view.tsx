'use client'

import { DnDTaskType as CardType, DnDTaskType } from '@/components/kanban-dnd/types'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import DepartmentChip from '@/components/ui/department-chip'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { Flame, Loader } from 'lucide-react'
import { DataTable } from '@/components/custom-data-table'
import { useRouter } from 'next/navigation'
import { formatDate } from '@/utils/format-date.utils'
import getStatusIcon from '@/components/get-status-icon'
import { useState, useMemo, useCallback, memo } from 'react'
import SelectionBanner, { BulkUpdateData } from '@/components/planner-bulk-actions'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { toast } from 'sonner'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError } from 'axios'

export type DnDTaskTypeWithStringId = Omit<DnDTaskType, 'id'> & {
    id: string
}

interface ListViewProps {
    items: CardType[]
    onTaskUpdate?: (task: CardType) => Promise<void>
    isUpdating: boolean
    isLoading: boolean
    refetchTasks: () => void
}

// Move columns outside component to prevent recreation
const createColumns = (): ColumnDef<DnDTaskTypeWithStringId>[] => [
    {
        id: 'select',
        header: ({ table }) => (
            <Checkbox
                checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                id="select"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'short_code',
        header: 'Task Id',
        cell: ({ row }) => {
            return (
                <div className="flex justify-center items-center gap-2 w-fit">
                    {row.original.taskDepartment && (
                        <DepartmentChip
                            shortCode={row.original.taskDepartment.short_code}
                            label={row.original.short_code}
                            size="sm"
                        />
                    )}
                </div>
            )
        },
    },
    {
        accessorKey: 'title',
        header: 'Task Name',
        cell: ({ row }) => {
            return (
                <div className="w-fit max-w-[200px] sm:max-w-[250px] md:max-w-[300px] lg:max-w-[350px] text-xs sm:text-sm font-medium mb-1">
                    {row.original.title}
                </div>
            )
        },
    },
    {
        accessorKey: 'taskStatus',
        header: 'Status',
        cell: ({ row }) => {
            const { label, colour } = row.original.taskStatus
            const newLabel = label
            return (
                <div className="flex items-center gap-2 pl-2" style={{ color: colour }}>
                    <div>{getStatusIcon(newLabel)}</div>
                    <p>{newLabel}</p>
                </div>
            )
        },
    },
    {
        accessorKey: 'assignedUser',
        header: 'Assignee',
        cell: ({ row }) => {
            const assignees = row.original?.taskAssignees
            return (
                <div className="flex -space-x-2">
                    {assignees && assignees.length > 0 ? (
                        <>
                            {assignees.slice(0, 3).map((member) => (
                                <AssigneeAvatar
                                    key={member.id}
                                    assignee={`${member.first_name} ${member.last_name}`}
                                    className="border-2 border-white h-[24px] w-[24px]"
                                />
                            ))}
                            {assignees.length > 3 &&
                                (() => {
                                    const extraCount = assignees.length - 3
                                    const extraLabel = `+ ${extraCount} ${extraCount > 1 ? 'Others' : 'Other'}`
                                    return (
                                        <AssigneeAvatar
                                            className="border-2 border-white h-[24px] w-[24px]"
                                            assignee={extraLabel}
                                        />
                                    )
                                })()}
                        </>
                    ) : (
                        <p className="text-xs text-[#3C557A]">Not Assigned</p>
                    )}
                </div>
            )
        },
    },
    {
        accessorKey: 'due_date',
        header: 'Due Date',
        cell: ({ row }) => {
            const formattedDate = row.original.due_date ? formatDate(new Date(row.original.due_date), 'DD-MM-YYYY') : null
            return formattedDate || 'Nill'
        },
    },
    {
        accessorKey: 'taskPriority',
        header: 'Priority',
        cell: ({ row }) => {
            return (
                <>
                    {row.original.taskPriority &&
                        (row.original.taskPriority.label.toLowerCase() === 'high' ? (
                            <div className="flex items-center text-sm">
                                <Flame color={row.original.taskPriority.color} size={14} />
                                <p style={{ color: row.original.taskPriority.color }}>{row.original.taskPriority.label}</p>
                            </div>
                        ) : (
                            row.original.taskPriority.label
                        ))}
                </>
            )
        },
    },
]

const ListViewComponent = ({ items, isLoading, refetchTasks }: ListViewProps) => {
    const router = useRouter()
    const [selectedRows, setSelectedRows] = useState<DnDTaskTypeWithStringId[]>([])
    const [shouldClearSelection, setShouldClearSelection] = useState(false)

    const bulkUpdateMutation = useMutation({
        mutationFn: async (payload: { taskIds: number[] } & BulkUpdateData) => {
            // replace with your API endpoint
            const { data } = await api.post(endpoints.tasks.bulkActions, payload)
            return data
        },
        onSuccess: async (response) => {
            // ✅ refetch tasks or show a toast
            toast.success(response.message || 'update success')
            await refetchTasks()
            // optional: router.refresh() if you use Next.js app router
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update tasks')
            throw error
        },
    })

    const bulkDeleteMutation = useMutation({
        mutationFn: async (payload: { taskIds: number[] }) => {
            // replace with your API endpoint
            const { data } = await api.delete(endpoints.tasks.bulkActions, { data: payload })
            return data
        },
        onSuccess: async () => {
            // ✅ refetch tasks or show a toast
            toast.success('delete success')
            await refetchTasks()
            // optional: router.refresh() if you use Next.js app router
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to delete tasks')
            throw error
        },
    })

    // Memoize columns to prevent recreation
    const columns = useMemo(() => createColumns(), [])

    // Memoize transformed data to prevent recreation on every render
    const transformedData = useMemo(() => items.map((item) => ({ ...item, id: item.id.toString() })), [items])

    // Memoize callbacks to prevent recreation
    const handleRowClick = useCallback(
        (row: DnDTaskTypeWithStringId) => {
            router.push(`task/${row.id}`)
        },
        [router],
    )

    const handleSelectionChange = useCallback(
        (rows: DnDTaskTypeWithStringId[]) => {
            setSelectedRows(rows)
            // Reset clear flag when selection changes
            if (shouldClearSelection && rows.length > 0) {
                setShouldClearSelection(false)
            }
        },
        [shouldClearSelection],
    )

    const clearSelection = useCallback(() => {
        setSelectedRows([])
        setShouldClearSelection(true)
        // Reset the flag after a brief delay to allow DataTable to process
        setTimeout(() => setShouldClearSelection(false), 0)
    }, [])

    const handleOnBulkUpdate = async (data: BulkUpdateData, selectedActionData: DnDTaskTypeWithStringId[]) => {
        const selectedtaskIds = selectedActionData.map((task) => Number(task.id))
        const payload = { taskIds: selectedtaskIds, ...data }
        await bulkUpdateMutation.mutateAsync(payload)
    }

    const handleOnBulkDelete = async (selectedActionData: DnDTaskTypeWithStringId[]) => {
        const selectedtaskIds = selectedActionData.map((task) => Number(task.id))
        const payload = { taskIds: selectedtaskIds }
        await bulkDeleteMutation.mutateAsync(payload)
    }

    // Memoize loading component to prevent unnecessary re-renders
    const loadingComponent = useMemo(
        () => (
            <div className="p-4 w-full flex items-center justify-center h-[40vh]">
                <Loader className="animate-spin" />
            </div>
        ),
        [],
    )

    // Memoize empty state component
    const emptyComponent = useMemo(
        () => <div className="p-4 w-full flex items-center justify-center h-[40vh] text-gray-500">No tasks found</div>,
        [],
    )

    // Early returns for loading and empty states
    if (isLoading) {
        return loadingComponent
    }

    if (items.length === 0) {
        return emptyComponent
    }

    return (
        <>
            {selectedRows.length > 0 && (
                <SelectionBanner
                    data={selectedRows}
                    selectedCount={selectedRows.length}
                    onClear={clearSelection}
                    className="w-fit max-w-full mb-1 bg-[#FFFFFF] border border-[#E2E8F0] rounded-[6px]"
                    onBulkUpdate={handleOnBulkUpdate}
                    onBulkDelete={handleOnBulkDelete}
                />
            )}
            <DataTable
                columns={columns}
                data={transformedData}
                onRowClick={handleRowClick}
                onSelectionChange={handleSelectionChange}
                clearSelection={shouldClearSelection}
            />
        </>
    )
}

// Memoize the entire component

export default memo(ListViewComponent)
