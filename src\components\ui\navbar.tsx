'use client'

import Link from 'next/link'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import Jo<PERSON><PERSON><PERSON>ingListCTA from '../../app/(site)/site-components/join-waiting-list-cta'
import { usePathname } from 'next/navigation'

export function Navbar() {
    const currentPath = usePathname()
    return (
        <div className="sticky top-0 left-0 sm:right-0 z-[100] flex justify-center p-4 sm:px-0 sm:pt-0">
            <nav
                className={cn(
                    `w-[95%] md:w-[80%] mt-4 mx-auto z-50 
                    flex items-center justify-between 
                    pr-4 py-2 md:py-4 
                    rounded-xl 
                    bg-white/40 
                    backdrop-blur-md 
                    border-b border-white/10 
                    shadow-[0_4px_10px_rgba(0,0,0,0.1),0_8px_20px_rgba(0,0,0,0.05)]
                    shadow-gray-500/10
                    transition-all duration-300 px-4`,
                    // 'flex items-center justify-between w-sm sm:w-full pr-2 py-1 rounded-lg sm:rounded-none transition-all duration-300 bg-[#303030]',
                )}>
                {/* Logo with Home Link */}
                <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={31} height={26} className="h-auto w-auto" />
                    <span className="font-semibold text-sm 3xl:text-[15px] text-[#000000]">Raydian</span>
                </Link>
                <div className="hidden lg:flex text-black text-sm font-semibold gap-4">
                    <Link href="/">About</Link>
                    <Link href="/pricing" className={currentPath === '/pricing' ? 'text-[#08B38B]' : ''}>
                        Pricing
                    </Link>
                    <Link href="/docs" className={currentPath === '/docs' ? 'text-[#08B38B]' : ''}>
                        Documentation
                    </Link>
                </div>

                {/* Navigation Links */}
                <div className="flex items-center gap-8">
                    {/* <Link
                        href={currentPath === '/' ? '#pricing' : '/#pricing'}
                        className="hidden md:block text-xs 3xl:text-sm font-normal 3xl:font-medium text-white transition-colors">
                        Pricing
                    </Link> */}
                    <JoinWaitingListCTA showActive />
                </div>
            </nav>
        </div>
    )
}
