'use client'

import { FC } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card'
import { format } from 'date-fns'
import { Progress } from './ui/progress'
import { Timer, Target, FolderOpen, Users } from 'lucide-react'
import featureIcon from '../../public/assets/icons/project-feat-icon.svg'
import Image from 'next/image'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'
import DepartmentChip from './ui/department-chip'

// Utility function to add ordinal suffix to the day
const getOrdinalSuffix = (day: number) => {
    if (day > 3 && day < 21) return 'th' // Covers 11th to 19th
    switch (day % 10) {
        case 1:
            return 'st'
        case 2:
            return 'nd'
        case 3:
            return 'rd'
        default:
            return 'th'
    }
}

export interface TeamMember {
    id: string
    first_name: string
    last_name: string
    img_url?: string
}

export interface ProjectStatus {
    label: string
    value: 'To-Do' | 'In Progress' | 'Completed' | 'Blocked' | 'Ongoing'
    colour: string
}

export interface DepartmentType {
    id: string
    short_code: string
    label: string
    icon?: string
}
export interface ProjectProgress {
    percentage: string
    completed: string
    total: string
}

export interface Project {
    id: string
    name: string
    overview: string
    status: ProjectStatus
    progress: ProjectProgress
    departments: DepartmentType[]
    createdAt?: string
    features?: {
        completed: number
        total: number
    }
    team: TeamMember[]
    likes?: number
    comments?: number
    stack: []
    img_url: string
    creator: string
}

interface ProjectCardProps {
    project: Project
    onClick?: (projectId: string) => void
}

const ProjectCard: FC<ProjectCardProps> = ({ project, onClick }) => {
    const handleCardClick = () => {
        if (onClick) {
            onClick(project.id)
        }
    }
    // Format the createdAt date
    const formattedDate = project.createdAt
        ? (() => {
              const date = new Date(project.createdAt)
              const day = format(date, 'd') // Get the day of the month
              const monthAndYear = format(date, 'MMMM yyyy') // Get the month and year
              return `${day}${getOrdinalSuffix(Number(day))} ${monthAndYear}`
          })()
        : null
    return (
        <Card className="w-full shadow-none hover:-translate-y-1 transition-all duration-200 cursor-pointer flex flex-col">
            <div onClick={handleCardClick}>
                <CardHeader>
                    <div className="flex justify-between">
                        <div className="flex gap-2">
                            <div className="border p-2 flex max-h-fit justify-center items-center rounded-md">
                                {project?.img_url ? (
                                    <Image
                                        src={project.img_url}
                                        alt="project image"
                                        width={16}
                                        height={16}
                                        className="rounded-full object-cover"
                                    />
                                ) : (
                                    <FolderOpen size={16} color="#9BA5B1" />
                                )}
                            </div>
                            <h3 className="text-lg font-semibold">{project.name}</h3>
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="flex flex-col px-4">
                    <div>
                        <div className="flex flex-col sm:flex-row my-2 sm:items-center w-full gap-1 sm:gap-0">
                            <p className="text-justify text-xs text-[#828282] shrink-0">Activity : </p>
                            <div className="flex flex-wrap items-center gap-1 sm:ml-2">
                                {project?.departments &&
                                    project.departments.map((dept) => (
                                        <DepartmentChip key={dept.id} shortCode={dept.short_code} label={dept.label} size="sm" />
                                    ))}
                            </div>
                        </div>
                        {Number(project?.progress?.percentage) >= 0 && (
                            <div className="mb-4">
                                <span className="text-xs text-[#828282]">Task progress</span>
                                <div className="w-full flex justify-between items-center text-sm mb-1 text-[12px]">
                                    <Progress value={Number(project?.progress?.percentage)} className="h-2 w-2/3" />
                                    <span className="text-[#3C557A] text-xs">{project?.progress.percentage}% Complete</span>
                                </div>
                            </div>
                        )}
                        {project?.status && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground gap-2 my-4">
                                <div className="flex items-center gap-2">
                                    <Target size={16} />
                                    <span className="text-[#202020] font-medium text-xs">Status </span>
                                </div>
                                <span className="ml-2 text-[12px]" style={{ color: project?.status?.colour || '#636E72' }}>
                                    {project?.status?.label}
                                </span>
                            </div>
                        )}

                        {formattedDate && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground my-4 gap-2">
                                <div className="flex items-center gap-2">
                                    <Timer size={16} />
                                    <span className="text-[#202020] font-medium text-xs">Due Date </span>
                                </div>
                                <span className="text-xs">
                                    <span className="ml-4">{formattedDate}</span>
                                </span>
                            </div>
                        )}

                        {project?.features && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground gap-2">
                                <div className="flex items-center gap-2">
                                    <Image
                                        src={featureIcon}
                                        alt="feature icon"
                                        width={16}
                                        height={16}
                                        className="rounded-full object-cover"
                                    />
                                    <span className="text-[#202020] font-medium text-xs">Features </span>
                                </div>
                                <span className="text-[12px] text-[#636E72]">
                                    <span className="ml-2 text-[12px] text-[#636E72]">
                                        {project?.progress?.completed}/{project?.progress?.total} Completed{' '}
                                    </span>
                                </span>
                            </div>
                        )}
                        {project?.team.length > 0 && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground mt-4 gap-2">
                                <div className="flex items-center gap-2">
                                    <Users size={16} />
                                    <span className="text-[#202020] font-medium text-xs">Assignee </span>
                                </div>
                                <div className="flex -space-x-2">
                                    {project?.team?.slice(0, 3)?.map((member) => (
                                        <AssigneeAvatar
                                            key={member.id}
                                            assignee={member.first_name + ' ' + member.last_name}
                                            className="border-2 border-white h-[24px] w-[24px]"
                                        />
                                    ))}
                                    {project?.team?.length > 3 && (
                                        <AssigneeAvatar
                                            className="border-2 border-white h-[24px] w-[24px]"
                                            assignee={`+ ${
                                                project.team.length - 3 > 1
                                                    ? project.team.length - 3 + 'Others'
                                                    : project.team.length - 3 + 'Other'
                                            }`}
                                        />
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </CardContent>
            </div>
        </Card>
    )
}

export default ProjectCard
