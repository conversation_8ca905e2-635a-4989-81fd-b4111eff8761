import React from 'react'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'
import { cn } from '@/lib/utils'

interface TeamMember {
    id: string | number
    first_name: string
    last_name: string
    img_url?: string
}

interface AvatarGroupProps {
    team?: TeamMember[]
    maxVisible?: number
    size?: number
    className?: string
}

const AvatarGroup: React.FC<AvatarGroupProps> = ({ team = [], maxVisible = 3, className = 'h-[24px] w-[24px]' }) => {
    const visibleMembers = team.slice(0, maxVisible)
    const remainingCount = team.length - maxVisible
    if (team.length === 0) return <div className="text-sm text-[#3C557A]">Nil</div>

    return (
        <div className="flex -space-x-2">
            {visibleMembers.map((member) => (
                <AssigneeAvatar
                    key={member.id}
                    assignee={`${member.first_name} ${member.last_name}`}
                    className={cn('border-2 border-white', className)}
                    imageUrl={member.img_url}
                />
            ))}
            {remainingCount > 0 && (
                <AssigneeAvatar
                    className={cn('border-2 border-white', className)}
                    assignee={`+${remainingCount} ${remainingCount > 1 ? ' Others' : ' Other'}`}
                />
            )}
        </div>
    )
}

export default AvatarGroup
