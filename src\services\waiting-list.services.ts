import { apiAuth } from '@/config/axios-config'
import { useMutation } from '@tanstack/react-query'
import endpoints from './api-endpoints'
import { toast } from 'sonner'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { AxiosError } from 'axios'

interface UseJoinWaitingListProps {
    onSuccess?: (email: string, data: JoinWaitingListResponse) => void
    onError?: (error: AxiosError) => void
}

interface JoinWaitingListRequest {
    email: string
}

interface JoinWaitingListResponse {
    message?: string
    // Add other expected response properties here
}

export const useJoinWaitingList = (props?: UseJoinWaitingListProps) => {
    const { onSuccess, onError } = props ?? {}

    return useMutation<JoinWaitingListResponse, AxiosError, string>({
        mutationFn: async (email: string) => {
            const payload: JoinWaitingListRequest = { email }
            const response = await apiAuth.post<JoinWaitingListResponse>(endpoints.waitingList.join, payload)
            return response.data
        },
        onSuccess: (data, email) => {
            // Call custom onSuccess if provided
            onSuccess?.(email, data)

            toast.success('Thanks for joining the waiting list!', {
                description: 'We will notify you when we launch',
                duration: 3000,
                icon: '👏',
                style: {
                    borderRadius: '10px',
                    background: '#08B38B',
                },
                position: 'bottom-center',
            })
        },
        onError: (error: AxiosError) => {
            // Call custom onError if provided
            onError?.(error)

            // Default error handling
            axiosErrorToast(error, 'Failed to join waiting list. Please try again.')
        },
    })
}
