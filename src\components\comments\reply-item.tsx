import { ArrowBigUp } from 'lucide-react'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { CommentType } from '.'
import CommentActions from './comment-action'
import EditComment from './edit-comment'
import { useAuthStore } from '@/store/auth.store'

const ReplyItem = ({
    comment,
    onUpvote,
    setEditingItem,
    isEditing,
    onAfterDeleteSuccess,
    editComment,
}: {
    comment: CommentType
    onUpvote: (commentId: number) => void
    setEditingItem: (state: CommentType | null) => void
    isEditing: boolean
    onAfterDeleteSuccess: () => void
    editComment: (data: { id: number; content: string }) => void
}) => {
    const { user } = useAuthStore()
    const userName = `${comment.creator.first_name} ${comment.creator.last_name}`
    const upvotedUserIds = comment?.upvotes.flatMap((upvote: { user_id: number }) => upvote.user_id)
    const isUpvoted = user?.id && upvotedUserIds?.includes(user?.id)

    return (
        <div className="flex gap-2 py-2 pl-4 pr-2 border-l w-full">
            <AssigneeAvatar
                assignee={userName}
                imageUrl={comment.creator.img_url || ''}
                className="border-2 border-white h-[25px] w-[25px]"
            />
            <div className="w-full">
                <div className="flex-1 space-y-2">
                    <div className="border rounded-[20px] bg-[#F0F2F5] p-2 overflow-auto max-w-[full]">
                        <div className="flex justify-between items-top w-full ">
                            <div className="flex gap-2 ">
                                <div className="text-sm text-gray-600">
                                    <p className="text-sm font-medium text-gray-900">
                                        <strong>{userName}</strong>

                                        <span className="text-xs text-[#1E1E1E99]">
                                            {' '}
                                            {new Date(comment?.updated_at) > new Date(comment?.created_at) ? '(edited)' : ''}
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <CommentActions
                                comments={comment}
                                onAftedDeleteSuccess={onAfterDeleteSuccess}
                                setEditingItem={setEditingItem}
                                isReply
                            />
                        </div>
                        {!isEditing ? (
                            <div className="text-sm text-[#1E1E1E] mb-2 w-full overflow-auto">{comment.content}</div>
                        ) : (
                            <div className="text-sm text-[#1E1E1E] my-2 ml-6">
                                <p
                                    className="text-xs text-end text-[#1E1E1E99] cursor-pointer pr-4 pb-2"
                                    onClick={() => setEditingItem(null)}>
                                    cancel
                                </p>
                                <EditComment comment={comment} editComment={editComment} />
                            </div>
                        )}
                    </div>
                    <div className="flex items-center gap-2 text-sm py-1">
                        <button
                            onClick={() => onUpvote(comment.id)}
                            className="flex cursor-pointer items-center gap-1 text-gray-500 hover:text-green-600 transition-colors">
                            <ArrowBigUp
                                size={18}
                                fill={isUpvoted ? '#2FA87A' : 'transparent'}
                                color={isUpvoted ? '#2FA87A' : '#000'}
                                strokeWidth={1}
                            />
                            <span className="text-xs">Upvote</span>
                        </button>
                        <span className="text-xs text-[#1E1E1E99]">{comment.time_ago}</span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ReplyItem
