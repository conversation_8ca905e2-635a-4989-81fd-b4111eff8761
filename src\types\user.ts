export interface TeamMemberRole {
    id: number
    role: string
}

export interface UserEngagement {
    current_streak: number
    highest_streak: number
    monthly_score: number
    total_score: number
    badges: string[]
    task_completion: number
    total_time_spent: number
    streak_start_date: string
    skill_level: number
}
export interface User {
    id: number
    email: string
    first_name: string
    last_name: string
    img_url: string
    cover_image_url: string
    user_type: number
    bio: string
    team_member_role: TeamMemberRole
    user_engagement: UserEngagement
}
