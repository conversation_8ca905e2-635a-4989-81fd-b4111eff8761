import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
    CheckCircle,
    ArrowLeft,
    BookOpen,
    Zap,
    FileText,
    Code,
    ChevronRight,
    Search,
    ExternalLink,
    MessageCircle,
    Command,
} from 'lucide-react'

export default function GettingStartedPage() {
    return (
        <div className="min-h-screen bg-white">
            {/* SEO-Optimized Header */}
            <header className="border-b border-gray-200 sticky top-0 z-50 backdrop-blur-sm bg-white/95">
                <div className="w-full px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        {/* Left Section - Logo and Navigation */}
                        <div className="flex items-center gap-3 sm:gap-6">
                            {/* Logo */}
                            <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
                                <Image
                                    src="/assets/img/raydian-logo.png"
                                    alt="Raydian.ai Logo"
                                    width={28}
                                    height={24}
                                    className="h-auto w-auto"
                                />
                                <span className="font-semibold text-base sm:text-lg text-[#18181B]">Raydian</span>
                            </Link>

                            {/* Breadcrumb */}
                            <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
                                <span>/</span>
                                <Link href="/docs" className="hover:text-gray-900 transition-colors">
                                    Documentation
                                </Link>
                                <span>/</span>
                                <span className="text-gray-900 font-medium">Getting Started</span>
                            </div>
                        </div>

                        {/* Center Section - Search (Desktop Only) */}
                        <div className="hidden sm:flex flex-1 max-w-lg mx-4">
                            <div className="relative w-full">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search documentation..."
                                    className="w-full pl-10 pr-12 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 focus:bg-white transition-colors"
                                />
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                                    <kbd className="hidden md:inline-flex items-center px-1.5 py-0.5 border border-gray-200 rounded text-xs font-mono text-gray-500">
                                        <Command className="h-3 w-3 mr-1" />K
                                    </kbd>
                                </div>
                            </div>
                        </div>

                        {/* Right Section - Actions */}
                        <div className="flex items-center gap-2 sm:gap-3">
                            {/* Mobile Search Button */}
                            <Button variant="ghost" size="sm" className="sm:hidden p-2 hover:bg-gray-100 rounded-md">
                                <Search className="h-4 w-4 text-gray-600" />
                                <span className="sr-only">Search</span>
                            </Button>

                            {/* GitHub Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                    <span className="sr-only">GitHub</span>
                                </Link>
                            </Button>

                            {/* Support Link */}
                            <Button variant="ghost" size="sm" asChild className="hidden md:flex">
                                <Link href="/support" target="_blank" rel="noopener noreferrer">
                                    <MessageCircle className="h-4 w-4" />
                                    <span className="sr-only">Support</span>
                                </Link>
                            </Button>

                            {/* Get Started Button */}
                            <Button size="sm" asChild className="text-xs sm:text-sm">
                                <Link href="/sign-up">
                                    <span className="hidden sm:inline">Get Started</span>
                                    <span className="sm:hidden">Start</span>
                                    <ExternalLink className="ml-1 sm:ml-2 h-3 w-3" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex gap-8">
                    {/* Left Sidebar */}
                    <aside className="hidden lg:block w-64 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-1">
                                <div className="pb-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Documentation</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="/docs"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <BookOpen className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Overview</div>
                                                <div className="text-xs text-gray-500">Documentation hub</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/getting-started"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-blue-700 bg-blue-50 border border-blue-200 rounded-md">
                                            <Zap className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">Getting Started</div>
                                                <div className="text-xs text-blue-600">Quick setup guide</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <FileText className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">How-to Guides</div>
                                                <div className="text-xs text-gray-500">Step-by-step tutorials</div>
                                            </div>
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors group">
                                            <Code className="h-4 w-4" />
                                            <div>
                                                <div className="font-medium">API Reference</div>
                                                <div className="text-xs text-gray-500">Developer documentation</div>
                                            </div>
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Getting Started Steps</h3>
                                    <div className="space-y-1">
                                        <Link
                                            href="#step-1"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            1. Create Your Account
                                        </Link>
                                        <Link
                                            href="#step-2"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            2. Set Up First Project
                                        </Link>
                                        <Link
                                            href="#step-3"
                                            className="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                                            3. Start AI-Powered Planning
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>

                    {/* Main Content */}
                    <main className="flex-1 min-w-0">
                        <div className="max-w-4xl mx-auto">
                            {/* Breadcrumb Navigation */}
                            <nav className="mb-8">
                                <Link
                                    href="/docs"
                                    className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                                    <ArrowLeft className="h-4 w-4" />
                                    Back to Documentation
                                </Link>
                            </nav>

                            {/* Page Header */}
                            <div className="mb-8">
                                <h1 className="text-4xl font-bold text-[#18181B] mb-4">Getting Started with Raydian.ai</h1>
                                <p className="text-lg text-gray-600 leading-relaxed mb-6">
                                    Welcome to Raydian.ai! This comprehensive guide will help you get up and running quickly with
                                    our AI-powered project planning platform. Whether you&apos;re a project manager, team lead, or
                                    individual contributor, you&apos;ll learn how to leverage our intelligent automation to
                                    streamline your workflow and achieve better project outcomes.
                                </p>
                                <p className="text-lg text-gray-600 leading-relaxed">
                                    Our onboarding process is designed to get you productive within minutes, not hours. Follow
                                    these three essential steps to transform how you approach project planning and execution.
                                </p>
                            </div>

                            {/* Getting Started Steps */}
                            <div className="space-y-8">
                                <Card className="border border-[#0000001A] shadow-none rounded-[20px] hover:shadow-md transition-all duration-200">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center gap-3">
                                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                                Step 1
                                            </Badge>
                                            <CardTitle className="text-2xl font-semibold">Create Your Account</CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <p className="text-base text-gray-600 leading-relaxed">
                                            Sign up for a free account to get started with Raydian.ai. You&apos;ll get 100 free
                                            prompts to explore the platform and experience the power of AI-driven project
                                            planning. Our registration process is streamlined for immediate productivity.
                                        </p>
                                        <div className="bg-gray-50 rounded-lg p-4">
                                            <h4 className="text-base font-semibold text-gray-900 mb-3">What you&apos;ll need:</h4>
                                            <ul className="space-y-2 text-sm text-gray-600">
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Valid email address for account verification and notifications</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Strong password with minimum 8 characters and 2FA support</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Email verification to activate your account and security features</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Optional: Company information for team collaboration features</span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div className="bg-blue-50 rounded-lg p-4">
                                            <h4 className="text-base font-semibold text-blue-900 mb-2">Pro Tip</h4>
                                            <p className="text-sm text-blue-800">
                                                Enable two-factor authentication during signup for enhanced security. This will
                                                protect your projects and team data.
                                            </p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card className="border border-[#0000001A] shadow-none rounded-[20px] hover:shadow-md transition-all duration-200">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center gap-3">
                                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                                Step 2
                                            </Badge>
                                            <CardTitle className="text-2xl font-semibold">Set Up Your First Project</CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <p className="text-base text-gray-600 leading-relaxed">
                                            Create your first project and start planning with AI assistance. Our intelligent
                                            system will guide you through the setup process and help you define clear objectives
                                            and deliverables.
                                        </p>
                                        <div className="bg-gray-50 rounded-lg p-4">
                                            <h4 className="text-base font-semibold text-gray-900 mb-3">
                                                Project Setup Checklist:
                                            </h4>
                                            <ul className="space-y-2 text-sm text-gray-600">
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Be specific about your project goals and expected outcomes</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Include timeline requirements and key milestones</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Mention any constraints, resources, or dependencies</span>
                                                </li>
                                                <li className="flex items-start gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                                    <span>Define success criteria and measurable objectives</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card className="border border-[#0000001A] shadow-none rounded-[20px] hover:shadow-md transition-all duration-200">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center gap-3">
                                            <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                                                Step 3
                                            </Badge>
                                            <CardTitle className="text-2xl font-semibold">Start AI-Powered Planning</CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <p className="text-base text-gray-600 leading-relaxed">
                                            Use our AI-powered planning tools to break down your project into manageable tasks.
                                            Our intelligent algorithms will suggest optimal task sequences, resource allocation,
                                            and timeline estimates based on your project requirements.
                                        </p>
                                        <div className="bg-purple-50 rounded-lg p-4">
                                            <h4 className="text-base font-semibold text-purple-900 mb-2">What happens next?</h4>
                                            <p className="text-sm text-purple-800 mb-3">
                                                Our AI will analyze your project requirements and generate a comprehensive plan
                                                including:
                                            </p>
                                            <ul className="space-y-1 text-sm text-purple-800">
                                                <li>• Task breakdown and dependencies</li>
                                                <li>• Resource allocation recommendations</li>
                                                <li>• Timeline estimates and milestones</li>
                                                <li>• Risk assessment and mitigation strategies</li>
                                            </ul>
                                        </div>
                                        <Button className="w-full sm:w-auto" size="lg">
                                            Start Your First Project
                                        </Button>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Next Steps */}
                            <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-[20px] p-6">
                                <h3 className="text-xl font-semibold text-[#18181B] mb-4">Ready for more?</h3>
                                <p className="text-base text-gray-600 mb-4">
                                    Now that you&apos;ve completed the basic setup, explore our advanced features and guides.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-3">
                                    <Button variant="outline" asChild>
                                        <Link href="/docs/guides">
                                            View How-to Guides <ChevronRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                    <Button variant="outline" asChild>
                                        <Link href="/docs/api">
                                            API Documentation <ChevronRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </main>

                    {/* Right Sidebar */}
                    <aside className="hidden xl:block w-56 flex-shrink-0">
                        <div className="sticky top-24 max-h-screen overflow-y-auto">
                            <nav className="space-y-6">
                                <div>
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Quick Navigation
                                    </h3>
                                    <div className="space-y-2">
                                        <Link href="/docs/getting-started" className="block text-xs text-blue-600 font-medium">
                                            Getting Started
                                        </Link>
                                        <Link
                                            href="/docs/guides"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            How-to guides
                                        </Link>
                                        <Link
                                            href="/docs/api"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Integrations
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        On This Page
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="#step-1"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Create Your Account
                                        </Link>
                                        <Link
                                            href="#step-2"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Set Up First Project
                                        </Link>
                                        <Link
                                            href="#step-3"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Start AI-Powered Planning
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">
                                        Next Steps
                                    </h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/docs/guides/creating-first-project"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Creating First Project
                                        </Link>
                                        <Link
                                            href="/docs/guides/ai-prompt-optimization"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            AI Prompt Optimization
                                        </Link>
                                        <Link
                                            href="/docs/guides/team-collaboration"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Team Collaboration
                                        </Link>
                                    </div>
                                </div>

                                <div className="border-t border-gray-200 pt-4">
                                    <h3 className="text-xs font-semibold text-gray-900 uppercase tracking-wide mb-3">Help</h3>
                                    <div className="space-y-2">
                                        <Link
                                            href="/support"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Contact Support
                                        </Link>
                                        <Link
                                            href="/docs/guides/troubleshooting"
                                            className="block text-xs text-gray-600 hover:text-gray-900 transition-colors">
                                            Troubleshooting
                                        </Link>
                                    </div>
                                </div>
                            </nav>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    )
}
