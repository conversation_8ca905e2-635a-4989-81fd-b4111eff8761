import { motion } from 'framer-motion'

interface WelcomeHeaderProps {
    userName?: string
    hasMessages: boolean
}

export const WelcomeHeader = ({ userName, hasMessages }: WelcomeHeaderProps) => {
    return (
        <div className={hasMessages ? 'p-4 pb-0' : 'text-center'}>
            <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-gray-500 font-medium dark:text-gray-400 mb-2">
                {userName ? `Welcome, ${userName}` : 'Welcome'}
            </motion.h2>

            <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className={`text-3xl font-medium mb-4 flex flex-row ${hasMessages ? '' : 'justify-center'}`}>
                <span className="text-3xl font-light bg-gradient-to-r from-[#08698F] to-[#68CC58] text-transparent bg-clip-text">
                    Task Insights
                </span>
                <span className="pl-2 text-black font-light dark:text-white">with Raydian AI</span>
            </motion.h1>
        </div>
    )
}
