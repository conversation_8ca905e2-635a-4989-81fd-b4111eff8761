const endpoints = Object.freeze({
    authentication: {
        signUp: '/auth/signup',
        signIn: '/auth/login',
        signOut: '/auth/logout',
        refresh: '/auth/refresh',
        verifyOtp: '/auth/verify-email',
        resendOtp: '/auth/resend-verification',
        forgotPassword: '/auth/forgot-password',
        resetPassword: '/auth/reset-password',
    },
    onboarding: {
        inviteTeam: '/invite-team',
        createWorkSpace: '/workspace',
    },
    user: {
        me: 'user/me',
        listUsers: '/user/',
        userByWorkspace: '/user/workspace/users',
        getUserById: '/user/:id',
        updateUserById: '/user/:id',
    },
    project: {
        createProject: '/project',
        listProjects: '/project',
        getTaskDistribution: '/project/task-distribution/department',
        deleteProject: '/project/:id',
        updateProject: '/project/:id',
        addUsersToProject: '/project/user',
    },
    tasks: {
        getTasks: '/task',
        createTask: '/task',
        updateTask: '/task',
        deleteTask: '/task',
        bulkActions: '/task/bulkActions',
    },
    llm: {
        chat: '/chat/',
        interactiveAgent: '/chat/interactive-agent',
        getCredits: '/chat/credits',
    },
    kanban: {
        getColumns: '/planner/columns',
    },
    meta: {
        getDepartments: '/meta/departments',
        getFeatures: '/meta/features',
        getProjects: '/meta/projects',
        getStatuses: '/meta/statuses',
        getPriorities: '/meta/priorities',
        getBugs: '/meta/bugs',
        getAssignees: '/meta/assignees',
        getDesignations: 'meta/roles',
        getTimezones: 'meta/timezones',
        getUserTypes: 'meta/user-types',
    },
    workspace: {
        join: '/workspace/join',
        invite: '/workspace/:id/invite',
        getWorkspaces: '/workspace',
        deleteWorkspace: '/workspace/:id',
        updateWorkspace: '/workspace/:id',
        updateUserType: '/workspace/user',
    },
    features: {
        getFeatures: '/feature',
        createFeature: '/feature',
        deleteFeature: '/feature',
    },
    waitingList: {
        join: '/waiting-list',
    },
    comments: {
        createComment: '/comment',
        getComments: '/comment/:entityType/:entityId',
        deleteComment: '/comment/:commentId',
        updateComment: '/comment/:commentId',
    },
    feedback: {
        submitFeedback: '/feedback',
    },
    notification: {
        listNotifications: '/notification/',
        markAsRead: '/notification/:id',
        markAsAllRead: '/notification/',
    },
    activityLog: {
        listActivityLogs: '/activity-log',
    },
    uploadFile: {
        getPresignedUrl: '/file-upload/presigned-url',
    },
})

export default endpoints
