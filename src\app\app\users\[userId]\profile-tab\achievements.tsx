import { User } from '@/types/user'
import Image from 'next/image'

const AchievementsTabContent = ({ user }: { user: User }) => {
    const engagement = user.user_engagement
    const badges = engagement?.badges || []
    return (
        <div>
            {badges.length > 0 ? (
                <div className="flex gap-2 flex-wrap">
                    {badges.map((badge, index) => (
                        <div key={index} className="bg-gray-100 px-2 py-1 rounded-full text-xs text-gray-500">
                            {badge}
                        </div>
                    ))}
                </div>
            ) : (
                <div className="flex gap-2 flex-wrap h-[400px] w-full justify-center items-center">
                    <div>
                        <Image
                            src="/assets/img/empty-awards.webp"
                            width={192}
                            height={192}
                            alt="now awards"
                            className="object-cover mx-auto"
                        />
                        <p className="text-[16px] font-semibold text-black mt-3">Keep Working ! No Achievements Yet .</p>
                    </div>
                </div>
            )}
        </div>
    )
}

export default AchievementsTabContent
