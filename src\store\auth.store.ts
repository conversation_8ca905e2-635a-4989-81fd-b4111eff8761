import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { api, apiAuth } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { AxiosError } from 'axios'

interface User {
    id: number
    email: string
    first_name: string
    last_name: string
    img_url: string
    workspaces: Workspace[]
    cover_image_url: string
    user_type: number
}

export interface Workspace {
    id: number
    name: string
    img_url: string
    invite_link: string
    plan: number
    user_type?: number
    is_admin?: boolean
}

interface AuthStore {
    // State
    user: User | null
    pendingVerificationEmail: string | null
    accessToken: string | null
    currentWorkspace: Workspace | null
    invitationUrl: string | null

    // Actions
    setPendingVerificationEmail: (email: string | null) => void
    setAccessToken: (token: string | null) => void
    setUser: (user: User | null) => void
    setCurrentWorkSpace: (workspace: Workspace | null) => void
    setInvitationUrl: (url: string | null) => void

    // Computed getters
    getUserId: () => number | null
    getActiveWorkspaceId: () => number | null

    // Async Actions
    login: (credentials: { email: string; password: string }) => Promise<{ success: boolean; message?: string }>
    oAuthLogin: (accessToken: string) => { success: boolean; message?: string }
    logout: () => Promise<void>
    verify: (credentials: { email: string; code: string }) => Promise<{ success: boolean; message?: string }>
    fetchMyDetails: () => Promise<void>
}

export const useAuthStore = create<AuthStore>()(
    persist(
        (set, get) => ({
            user: null,
            pendingVerificationEmail: null,
            accessToken: null,
            currentWorkspace: null,
            invitationUrl: null,

            setPendingVerificationEmail: (email) => set({ pendingVerificationEmail: email }),
            setAccessToken: (token) => set({ accessToken: token }),
            setUser: (user) => set({ user }),
            setCurrentWorkSpace: (workspace) => set({ currentWorkspace: workspace }),
            setInvitationUrl: (url) => set({ invitationUrl: url }),

            getUserId: () => {
                const state = get()
                return state.user?.id || null
            },

            getActiveWorkspaceId: () => {
                const state = get()
                return state.currentWorkspace?.id || null
            },

            login: async (credentials) => {
                try {
                    const response = await apiAuth.post(endpoints.authentication.signIn, credentials)
                    set({ accessToken: response.data.accessToken })

                    document.cookie = 'auth_session=true; path=/; max-age=86400; samesite=strict; secure'
                    return { success: true }
                } catch (error) {
                    const err = error as AxiosError<{ message: string }>
                    return {
                        success: false,
                        message: err.response?.data?.message || 'Login failed',
                    }
                }
            },
            verify: async ({ email, code }) => {
                try {
                    if (!email) throw new Error('Email is required')
                    const response = await apiAuth.post(endpoints.authentication.verifyOtp, {
                        email,
                        code,
                    })
                    set({ accessToken: response.data.accessToken })

                    document.cookie = 'auth_session=true; path=/; max-age=86400; samesite=strict; secure'
                    return { success: true }
                } catch (error) {
                    const err = error as AxiosError<{ message: string }>
                    return {
                        success: false,
                        message: err.response?.data?.message || 'Verification failed',
                    }
                }
            },
            oAuthLogin: (accessToken: string) => {
                try {
                    set({ accessToken })

                    document.cookie = 'auth_session=true; path=/; max-age=86400; samesite=strict; secure'
                    return { success: true }
                } catch (error) {
                    const err = error as AxiosError<{ message: string }>
                    return {
                        success: false,
                        message: err.response?.data?.message || 'Login failed',
                    }
                }
            },
            fetchMyDetails: async () => {
                try {
                    const response = await api.get(endpoints.user.me)
                    set((state) => {
                        const hasCurrentWorkspace = !!state.currentWorkspace && Object.keys(state.currentWorkspace).length > 0
                        const workspaces = response.data.user.workspaces
                        if (hasCurrentWorkspace && state.currentWorkspace) {
                            return {
                                user: response.data.user,
                                currentWorkspace: workspaces.find(
                                    (workspace: Workspace) => workspace.id === state.currentWorkspace?.id,
                                ),
                            }
                        }

                        return {
                            user: response.data.user,
                            currentWorkspace: response?.data?.user?.workspaces?.[0] || null,
                        }
                    })
                } catch (error) {
                    console.error('Fetching user details failed:', error)
                    set({ user: null })
                }
            },

            logout: async () => {
                try {
                    await apiAuth.post(endpoints.authentication.signOut)
                    document.cookie = 'auth_session=; path=/; max-age=0'
                    set({
                        user: null,
                        accessToken: null,
                        pendingVerificationEmail: null,
                        currentWorkspace: null,
                    })
                } catch (error) {
                    console.error('Logout failed:', error)
                }
            },
        }),
        {
            name: 'auth-storage',
            storage: createJSONStorage(() => localStorage),
            partialize: (state) => ({
                pendingVerificationEmail: state.pendingVerificationEmail,
                user: state.user,
                currentWorkspace: state.currentWorkspace,
                invitationUrl: state.invitationUrl,
                // accessToken: state.accessToken,
            }),
        },
    ),
)
