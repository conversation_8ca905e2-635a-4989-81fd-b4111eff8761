import type { Metadata } from 'next'

export const metadata: Metadata = {
    title: 'Documentation Hub - Raydian.ai | AI-Powered Project Planning Guides',
    description:
        'Master AI-powered project planning with Raydian.ai documentation. Quick start guides, advanced tutorials, API reference, team collaboration tips, and troubleshooting help.',
    keywords:
        'Raydian.ai documentation, AI project management, project planning software, how-to guides, API documentation, team collaboration, project management tutorials, AI planning tools',
    openGraph: {
        title: 'Documentation Hub - Raydian.ai | AI-Powered Project Planning Guides',
        description:
            'Master AI-powered project planning with comprehensive guides, tutorials, and API documentation for Raydian.ai.',
        type: 'website',
        url: 'https://raydian.ai/docs',
        siteName: 'Raydian.ai',
        images: [
            {
                url: 'https://raydian.ai/assets/img/docs-og-image.jpg',
                width: 1200,
                height: 630,
                alt: 'Raydian.ai Documentation Hub',
            },
        ],
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Documentation Hub - Raydian.ai | AI-Powered Project Planning Guides',
        description:
            'Master AI-powered project planning with comprehensive guides, tutorials, and API documentation for Raydian.ai.',
        images: ['https://raydian.ai/assets/img/docs-twitter-card.jpg'],
    },
    alternates: {
        canonical: 'https://raydian.ai/docs',
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
}

export default function DocsLayout({ children }: { children: React.ReactNode }) {
    const jsonLd = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'Raydian.ai Documentation Hub',
        description: 'Comprehensive documentation hub for Raydian.ai AI-powered project planning platform',
        url: 'https://raydian.ai/docs',
        publisher: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
            logo: {
                '@type': 'ImageObject',
                url: 'https://raydian.ai/assets/img/raydian-logo.png',
                width: 200,
                height: 60,
            },
        },
        potentialAction: {
            '@type': 'SearchAction',
            target: {
                '@type': 'EntryPoint',
                urlTemplate: 'https://raydian.ai/docs/search?q={search_term_string}',
            },
            'query-input': 'required name=search_term_string',
        },
        mainEntity: {
            '@type': 'ItemList',
            name: 'Documentation Sections',
            description: 'Main documentation categories for Raydian.ai',
            numberOfItems: 3,
            itemListElement: [
                {
                    '@type': 'SiteNavigationElement',
                    position: 1,
                    name: 'Getting Started',
                    description: 'Quick setup guide for new users',
                    url: 'https://raydian.ai/docs/getting-started',
                },
                {
                    '@type': 'SiteNavigationElement',
                    position: 2,
                    name: 'How-to Guides',
                    description: 'Step-by-step tutorials and best practices',
                    url: 'https://raydian.ai/docs/guides',
                },
                {
                    '@type': 'SiteNavigationElement',
                    position: 3,
                    name: 'API Reference',
                    description: 'Complete developer documentation and API reference',
                    url: 'https://raydian.ai/docs/api',
                },
            ],
        },
        datePublished: '2024-01-01',
        dateModified: new Date().toISOString(),
        inLanguage: 'en-US',
    }

    return (
        <>
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
            {children}
        </>
    )
}
