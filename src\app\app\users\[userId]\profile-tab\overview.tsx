import { Button } from '@/components/ui/button'
import { Card, CardHeader } from '@/components/ui/card'
import { User, UserEngagement } from '@/types/user'
import { ChartColumn, Flame, Info, SquareArrowDown, Target, Trophy, Wallet } from 'lucide-react'
import { memo, useMemo } from 'react'
import SkillProgressBar from './custom-progress-bar'
import Image from 'next/image'
import { CustomReusableTooltip } from '@/components/custom-tooltip'

const DAY_MAP = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'] as const

const StatCard = ({
    icon: Icon,
    iconColor,
    label,
    value,
}: {
    icon: React.ComponentType<{ className?: string }>
    iconColor: string
    label: string
    value: number | string
}) => (
    <div className="border border-gray-200 rounded-xl bg-white p-4  transition-shadow hover:shadow-sm">
        <Icon className={`h-4 w-4 ${iconColor}`} />
        <p className="text-xs text-gray-500 mt-3">{label}</p>
        <p className="text-black text-lg font-medium">{label === 'Task Completion' ? `${value}%` : value}</p>
    </div>
)

const StreakIndicator = ({ currentStreak, streakStartDate }: { currentStreak: number; streakStartDate: string }) => {
    const start = new Date(streakStartDate)

    // All streak dates
    const streakDates = Array.from({ length: currentStreak }, (_, i) => {
        const d = new Date(start)
        d.setDate(start.getDate() + i)
        return d.toDateString()
    })

    // Current week Monday
    const today = new Date()
    const dayOfWeek = today.getDay() // 0=Sun,1=Mon...
    const monday = new Date(today)
    monday.setDate(today.getDate() - ((dayOfWeek + 6) % 7)) // back to Monday

    // Generate this week’s Mon–Fri
    const weekDays = Array.from({ length: 5 }, (_, i) => {
        const d = new Date(monday)
        d.setDate(monday.getDate() + i)
        return d
    })

    return (
        <div className="grid grid-cols-5 gap-2 p-4">
            {weekDays.map((date, index) => {
                const hasStreak = streakDates.includes(date.toDateString())

                return (
                    <div key={index} className="text-center">
                        {hasStreak ? (
                            <div
                                className="aspect-square rounded-full flex justify-center items-center bg-orange-100 transition-all duration-300 hover:scale-105 mx-auto"
                                aria-label={`Streak day ${DAY_MAP[index]}`}>
                                <Image
                                    width={24}
                                    height={24}
                                    src="/assets/icons/flame.svg"
                                    className="h-[22px] w-[22px]"
                                    alt="flame"
                                />
                            </div>
                        ) : (
                            <div
                                className="aspect-square rounded-full flex justify-center items-center bg-gray-100 mx-auto"
                                aria-label={`No streak on ${DAY_MAP[index]}`}>
                                <div className="h-[22px] w-[22px]" />
                            </div>
                        )}
                        <p className="text-sm text-[#737373] mt-2">{DAY_MAP[index]}</p>
                    </div>
                )
            })}
        </div>
    )
}

// Main engagement component with memoization
const Engagement = memo(({ engagement }: { engagement: UserEngagement }) => {
    // Memoize expensive calculations
    const stats = useMemo(
        () => [
            {
                icon: SquareArrowDown,
                iconColor: 'text-green-600',
                label: 'Total time spent',
                value: engagement?.total_time_spent || 0,
            },
            {
                icon: Wallet,
                iconColor: 'text-blue-600',
                label: 'Task Completion',
                value: engagement?.task_completion || 0,
            },
            {
                icon: SquareArrowDown,
                iconColor: 'text-green-600',
                label: 'Best Streak',
                value: engagement?.highest_streak || 0,
            },
        ],
        [engagement?.total_time_spent, engagement?.task_completion, engagement?.highest_streak],
    )

    const isStreakActive = useMemo(() => (engagement?.current_streak || 0) > 0, [engagement?.current_streak])

    return (
        <div className="bg-[#F0F0F0] p-4 rounded-[22px] space-y-3 w-full">
            {/* Overview Stats Card */}
            <Card className="shadow-sm p-0">
                <div className="space-y-3 px-4 pt-4">
                    <CardHeader className="border-b !px-2 text-sm font-medium !pb-1">
                        <span className="flex items-center gap-2">
                            <ChartColumn className="h-4 w-4" aria-hidden="true" />
                            Overview
                        </span>
                    </CardHeader>
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mx-auto w-full pb-4">
                        {stats.map((stat, index) => (
                            <StatCard
                                key={`${stat.label}-${index}`}
                                icon={stat.icon}
                                iconColor={stat.iconColor}
                                label={stat.label}
                                value={stat.value}
                            />
                        ))}
                    </div>
                </div>
            </Card>

            {/* Score Card */}
            <Card className="shadow-sm p-4">
                <div>
                    <CardHeader className="p-0 text-sm font-medium flex justify-between items-center">
                        <span className="flex items-center gap-2">
                            <Target className="h-4 w-4" aria-hidden="true" />
                            Score
                        </span>
                        <CustomReusableTooltip
                            heading="Breakdown:"
                            description={
                                <ul className="list-disc pl-5 space-y-1 text-xs text-gray-700">
                                    <li>Review Completed: +5 points</li>
                                    <li>In Progress → Review: +3 points</li>
                                    <li>Backlog → In Progress: +2 points</li>
                                    <li>Task assigned to user: +5 points</li>
                                </ul>
                            }>
                            <Button
                                size="icon"
                                variant="outline"
                                aria-label="Score information"
                                className="hover:bg-gray-50 h-[31px] w-[31px]">
                                <Info className="h-4 w-4" />
                            </Button>
                        </CustomReusableTooltip>
                    </CardHeader>
                    <div className="border border-gray-200 mt-2  p-4 rounded-xl w-fit mb-2">
                        <span className="text-lg font-medium">{engagement?.total_score || 0}</span>
                    </div>
                </div>
                <div className="pb-4">
                    <SkillProgressBar value={engagement?.skill_level || 0} />
                </div>
            </Card>

            {/* Current Streak Card */}
            <Card className="shadow-sm">
                <div>
                    <CardHeader className="mx-4 !p-0 text-sm font-medium">
                        <div className="flex justify-between items-center">
                            <p className="flex items-center gap-2">
                                <Flame className="h-4 w-4" aria-hidden="true" />
                                Current Streak {engagement?.current_streak || 0}
                            </p>
                            <div className="flex gap-2 items-center">
                                <CustomReusableTooltip
                                    heading="How Your Streak Works"
                                    description="Your streak continues when you make a meaningful contribution each working day (Monday to Friday). A contribution includes creating or updating a task, adding a comment, or capturing a new idea.">
                                    <Button
                                        size="icon"
                                        variant="outline"
                                        aria-label="Score information"
                                        className="hover:bg-gray-50 h-[31px] w-[31px]">
                                        <Info className="h-4 w-4" />
                                    </Button>
                                </CustomReusableTooltip>
                                {isStreakActive && (
                                    <p
                                        className="flex h-[23px] w-[23px] text-[8px] bg-green-50 items-center justify-center px-2 py-1 text-xs text-green-700 border border-green-200 rounded-full"
                                        role="status"
                                        aria-live="polite">
                                        Live
                                    </p>
                                )}
                            </div>
                        </div>
                    </CardHeader>

                    <p className="mx-4">
                        <span className="flex items-center gap-2 text-xs text-gray-500">
                            <Trophy className="h-4 w-4" aria-hidden="true" />
                            Best Streak: {engagement?.highest_streak || 0}
                        </span>
                    </p>
                </div>
                <StreakIndicator
                    currentStreak={engagement?.current_streak || 0}
                    streakStartDate={engagement?.streak_start_date || ''}
                />
            </Card>
        </div>
    )
})

Engagement.displayName = 'Engagement'

// Main component with proper prop validation and memoization
const OverviewTabContent = memo(({ user }: { user: User }) => {
    const engagement = user.user_engagement
    const badges = engagement?.badges || []

    return (
        <div className="flex w-full justify-between gap-4">
            <div className="w-[50%] flex-shrink-0 mt-2">
                <p className="text-black text-base leading-relaxed text-left">{user.bio || 'No bio available'}</p>
                <div>
                    <p className="text-sm text-[#000000] font-medium mt-8">Awards</p>
                    {badges.length > 0 ? (
                        <div className="flex gap-2 flex-wrap">
                            {badges.map((badge, index) => (
                                <div key={index} className="bg-gray-100 px-2 py-1 rounded-full text-xs text-gray-500">
                                    {badge}
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex gap-2 flex-wrap h-[400px] w-full justify-center items-center">
                            <div>
                                <Image
                                    src="/assets/img/empty-awards.webp"
                                    width={192}
                                    height={192}
                                    alt="now awards"
                                    className="object-cover mx-auto"
                                />
                                <p className="text-[16px] font-semibold text-black mt-3">Keep Working ! No Achievements Yet .</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div className="w-[48%] flex-shrink-0">
                <Engagement engagement={engagement} />
            </div>
        </div>
    )
})

OverviewTabContent.displayName = 'OverviewTabContent'

export default OverviewTabContent
