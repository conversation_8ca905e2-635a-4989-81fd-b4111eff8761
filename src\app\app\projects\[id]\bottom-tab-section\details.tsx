'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import AvatarGroup from '@/components/avatar-group'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { <PERSON>R<PERSON>, Pencil, UserRoundPlus } from 'lucide-react'
import { formatDate } from '@/utils/format-date.utils'
import { DepartmentType } from '@/components/project-card'
import { StatusSelect } from '@/components/select-status'
import endpoints from '@/services/api-endpoints'
import AddUsersModal from '@/components/add-users.modal'
import { useMutation } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import { useEffect, useRef, useState } from 'react'

interface ProjectData {
    id: number
    name: string
    overview: string
    workspace_id: number
    status_id: number
    stack: Array<string>
    created_at: string
    updatedAt: string
    deletedAt: null
    created_by: number
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    status: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    features: {
        completed: number
        total: number
    }
    departments: DepartmentType[]
    team: Array<{
        id: number
        first_name: string
        last_name: string
        avatar?: string
    }>
    estimated_effort: string
    timeline: {
        start_date: string
        end_date: string
        days: number
    }
    priority: {
        level: string
        color: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
    }
}

const ProjectDetailsTab = ({ project, refetchProject }: { project: ProjectData; refetchProject: () => void }) => {
    const [overview, setOverview] = useState(project.overview)
    const textAreaRef = useRef<HTMLTextAreaElement>(null)
    const [isEdit, setIsEdit] = useState(false)

    useEffect(() => {
        if (isEdit && textAreaRef.current) {
            const el = textAreaRef.current
            el.focus()
            el.setSelectionRange(el.value.length, el.value.length) // Move cursor to end
        }
    }, [isEdit, textAreaRef])

    useEffect(() => {
        setOverview(project.overview)
    }, [isEdit, project.overview])

    const addUserToProjectMutation = useMutation({
        mutationFn: async (data: { project_id: number; user_ids: number[] }) => {
            const response = await api.post(endpoints.project.addUsersToProject, data)
            return response.data
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to add users')
        },
    })

    const saveMutation = useMutation({
        mutationFn: async (data: { project_id: number; overview: string }) => {
            const response = await api.put(endpoints.project.updateProject.replace(':id', project.id.toString()), data)
            return response.data
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update project')
        },
    })

    const handleSave = () => {
        if (!overview.trim() || overview.toLocaleLowerCase().trim() === project.overview.toLocaleLowerCase().trim()) {
            return
        }

        const body = {
            project_id: project.id,
            overview,
        }
        saveMutation.mutate(body, {
            onSuccess: () => {
                toast.success('Project updated successfully')
                setIsEdit(false)
                refetchProject()
            },
        })
    }

    const handleCancel = () => {
        setOverview(project.overview)
        setIsEdit(false)
    }

    const onEnterPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleSave()
        }
    }

    return (
        <Card className="w-full shadow-none border-none bg-transparent pt-0 px-0">
            <CardContent className="py-4 px-0">
                <div className="space-y-4">
                    <div className="grid grid-cols-[auto_1fr] gap-x-4 gap-y-4 text-[#3C557A] text-xs">
                        <div className="text-xs">Status</div>
                        <div className="flex items-center">
                            <div className="flex items-center gap-2">
                                {/* <div>{getStatusIcon(project.status.label)}</div>
                                <span className="text-xs">{project.status.label}</span> */}
                                <StatusSelect
                                    entityId={project.id}
                                    entityType="project"
                                    initialStatusId={project.status_id}
                                    fetchEndpoint={endpoints.meta.getStatuses}
                                    updateEndpoint={endpoints.project.updateProject.replace(':id', project.id.toString())}
                                    className="w-[120px] h-[22px] border-none"
                                    status_key="status_id"
                                    onStatusChange={() => refetchProject()}
                                />
                            </div>
                        </div>

                        <div className="text-xs">Team members</div>
                        <div className="flex gap-4 items-center">
                            <div className="flex -space-x-2">
                                <AvatarGroup team={project.team} />
                            </div>
                            <AddUsersModal
                                alreadyAssignedUsers={project.team?.map((member) => member.id)}
                                onSubmit={(users, closeModal) => {
                                    addUserToProjectMutation.mutate(
                                        {
                                            project_id: project.id,
                                            user_ids: users,
                                        },
                                        {
                                            onSuccess: () => {
                                                closeModal()
                                                toast.success('Users added successfully')
                                            },
                                            onSettled: () => {
                                                refetchProject()
                                            },
                                        },
                                    )
                                }}
                                dialogueTitle="Update Team Members"
                                dialogueDescription="Update team members for the project."
                                buttonText="Update">
                                <UserRoundPlus className="h-4 w-4" color="#939393" />
                            </AddUsersModal>
                        </div>

                        <div className="text-xs">Est. Effort</div>
                        {project?.estimated_effort && <div className="text-xs">{project?.estimated_effort} Hours</div>}

                        {project?.timeline && (
                            <>
                                <div className="text-xs">Projected Timeline</div>
                                <div className="text-xs flex items-center gap-2">
                                    {formatDate(project?.timeline?.start_date, 'DD-MM-YYYY')} <ArrowRight className="h-4 w-4" />{' '}
                                    {formatDate(project?.timeline?.end_date, 'DD-MM-YYYY')} ({project?.timeline?.days} Days)
                                </div>
                            </>
                        )}
                        <div className="text-xs">Tech Stack</div>
                        <div className="flex flex-wrap gap-2">
                            {project?.stack.length > 0 &&
                                project?.stack?.map((tech: string, index: number) => (
                                    <Badge key={index} variant="outline" className="text-xs bg-[#F1F5F9] border-none">
                                        {tech}
                                    </Badge>
                                ))}
                        </div>

                        <div className="text-xs">Created by</div>
                        <div className="flex items-center gap-2">
                            <AssigneeAvatar
                                assignee={project.creator.first_name + ' ' + project.creator.last_name}
                                imageUrl={project.creator.img_url}
                            />
                            <span className="text-xs">
                                {project.creator.first_name + ' ' + project.creator.last_name} on{' '}
                                {new Date(project.created_at).toLocaleDateString('en-US', {
                                    month: '2-digit',
                                    day: '2-digit',
                                    year: 'numeric',
                                })}
                            </span>
                        </div>
                    </div>
                    <div className="space-y-2 w-full">
                        <div className="flex items-center gap-2 text-xs text-[#3C557A]">
                            <p>Description</p>
                            <p className="cursor-pointer" onClick={() => setIsEdit(!isEdit)}>
                                <Pencil size={12} color="#9B9B9B" />
                            </p>
                        </div>
                        {!isEdit ? (
                            <div className="text- text-[#9095A1] line-clamp-2 hover:line-clamp-none">{project.overview}</div>
                        ) : (
                            <div className="space-y-2">
                                <textarea
                                    ref={textAreaRef}
                                    className="w-full h-[100px] border border-gray-300 rounded-md p-2"
                                    value={overview}
                                    onChange={(e) => setOverview(e.target.value)}
                                    onKeyDown={onEnterPress}
                                />
                                <div className="flex">
                                    <button className="px-4 rounded-md cursor-pointer" onClick={handleSave}>
                                        save
                                    </button>

                                    <button className=" px-4 rounded-md cursor-pointer" onClick={handleCancel}>
                                        cancel
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}

export default ProjectDetailsTab
