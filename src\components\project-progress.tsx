import type React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, FolderOpen } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'

interface ProjectProgressProps {
    title: string
    progressPercentage: number
    currentDay: number
    totalDays: number
    isOnTrack: boolean
    metrics: {
        features: number
        tasks: number
        teamMembers: number
    }
    projectStatusText: string
}

export const ProjectProgress: React.FC<ProjectProgressProps> = ({
    title,
    progressPercentage,
    currentDay,
    totalDays,
    isOnTrack,
    projectStatusText,
}) => {
    return (
        <Card className="w-full bg-[#FFFFFF61] shadow-none pt-0 overflow-hidden rounded-[8px] pb-8">
            <CardHeader className="bg-[#F2F5F8] p-3">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between ">
                    <div className="flex items-center gap-2">
                        <div className="p-[7px] rounded border border-[#E8E8ED] flex items-center justify-center">
                            <FolderOpen className="h-[17px] w-[17px]" />
                        </div>
                        <h2 className="text-xl font-semibold">{title}</h2>
                    </div>
                    <div className="mt-2 sm:mt-0 bg-[#FFFFFF] border border-dashed border-[#E2E8F0] rounded-md px-2 py-1">
                        <span className="text-sm">Projected Time line</span>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="px-6">
                <div className="flex flex-col space-y-6">
                    {/* Progress Section */}
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-sm">Projected progress</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Progress value={progressPercentage} className="h-4 rounded-[2px]" />
                            <span className="text-sm text-[#087A91]">{progressPercentage}%</span>
                            <span className="text-sm"> complete</span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm">
                                Workdays {currentDay}/{totalDays}
                            </span>
                            <span className="text-xs text-muted-foreground">→</span>
                            <span className="text-sm">{projectStatusText}</span>
                            {isOnTrack && (
                                <span className="flex items-center justify-center rounded-full w-4 h-4">
                                    <CircleCheck className="h-4 w-4 text-[#08B38B]" />
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Metrics */}
                    {/* <div className="flex flex-row gap-4">
                        <MetricCard value={metrics.features} label="Features" />
                        <MetricCard value={metrics.tasks} label="Tasks" />
                        <MetricCard value={metrics.teamMembers} label="Team Members" />
                    </div> */}
                </div>
            </CardContent>
        </Card>
    )
}
