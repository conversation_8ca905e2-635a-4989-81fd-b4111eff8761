import type { Metadata } from 'next'

export const metadata: Metadata = {
    title: 'How-to Guides - Raydian.ai Documentation',
    description:
        'Comprehensive how-to guides for Raydian.ai. Learn project creation, advanced planning, AI prompt optimization, team collaboration, troubleshooting, and API integration.',
    keywords:
        'Raydian.ai guides, how-to tutorials, project management guides, AI planning tutorials, team collaboration, API integration, troubleshooting guide',
    openGraph: {
        title: 'How-to Guides - Raydian.ai Documentation',
        description:
            'Comprehensive how-to guides for Raydian.ai. Learn project creation, advanced planning, AI prompt optimization, team collaboration, and more.',
        type: 'website',
        url: 'https://raydian.ai/docs/guides',
        siteName: 'Raydian.ai',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'How-to Guides - Raydian.ai Documentation',
        description:
            'Comprehensive how-to guides for Raydian.ai. Learn project creation, advanced planning, AI prompt optimization, team collaboration, and more.',
    },
    alternates: {
        canonical: 'https://raydian.ai/docs/guides',
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
        },
    },
}

export default function GuidesLayout({ children }: { children: React.ReactNode }) {
    const jsonLd = {
        '@context': 'https://schema.org',
        '@type': 'CollectionPage',
        name: 'Raydian.ai How-to Guides',
        description:
            'Comprehensive collection of how-to guides and tutorials for Raydian.ai AI-powered project planning platform',
        url: 'https://raydian.ai/docs/guides',
        mainEntity: {
            '@type': 'ItemList',
            name: 'How-to Guides',
            description: 'Step-by-step tutorials for Raydian.ai features',
            numberOfItems: 6,
            itemListElement: [
                {
                    '@type': 'Article',
                    position: 1,
                    name: 'Creating Your First Project',
                    description: 'Learn how to set up and configure your first project from scratch',
                    url: 'https://raydian.ai/docs/guides/creating-first-project',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
                {
                    '@type': 'Article',
                    position: 2,
                    name: 'Advanced Planning Features',
                    description: 'Discover advanced features to optimize your project planning workflow',
                    url: 'https://raydian.ai/docs/guides/advanced-planning',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
                {
                    '@type': 'Article',
                    position: 3,
                    name: 'AI Prompt Optimization',
                    description: 'Learn how to write effective prompts for AI-powered planning',
                    url: 'https://raydian.ai/docs/guides/ai-prompt-optimization',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
                {
                    '@type': 'Article',
                    position: 4,
                    name: 'Team Collaboration',
                    description: 'Best practices for team collaboration and communication',
                    url: 'https://raydian.ai/docs/guides/team-collaboration',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
                {
                    '@type': 'Article',
                    position: 5,
                    name: 'Troubleshooting Guide',
                    description: 'Common issues and solutions for Raydian.ai platform',
                    url: 'https://raydian.ai/docs/guides/troubleshooting',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
                {
                    '@type': 'Article',
                    position: 6,
                    name: 'API Integration Guide',
                    description: 'Complete guide to integrating with Raydian.ai REST API',
                    url: 'https://raydian.ai/docs/api',
                    author: {
                        '@type': 'Organization',
                        name: 'Raydian.ai',
                    },
                },
            ],
        },
        author: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
        },
        publisher: {
            '@type': 'Organization',
            name: 'Raydian.ai',
            url: 'https://raydian.ai',
            logo: {
                '@type': 'ImageObject',
                url: 'https://raydian.ai/assets/img/raydian-logo.png',
            },
        },
        datePublished: '2024-01-01',
        dateModified: new Date().toISOString(),
        mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': 'https://raydian.ai/docs/guides',
        },
    }

    return (
        <>
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
            {children}
        </>
    )
}
