import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import React, { useCallback, useRef, useEffect, useMemo } from 'react'
import { ScrollArea } from './ui/scroll-area'
import { useAuthStore } from '@/store/auth.store'
import { ArrowBigDown, Calendar, Loader } from 'lucide-react'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'
import { formatDate } from '@/utils/format-date.utils'
import { ActivityLogType } from '@/types/activity-log'
import { GetActivityIcon, GetActivityTitle } from './activity-log-helper-components'

type ActivityLogContainerPropType = {
    entityType: string
    entityId: number
}

// Memoized activity item component to prevent unnecessary re-renders
export const ActivityItem = React.memo(({ activity }: { activity: ActivityLogType }) => {
    // Memoize the formatted date to avoid recalculation
    const formattedDate = useMemo(
        () => formatDate(activity?.created_at, 'DD MMM YYYY', { showTime: true, timeFormat: '12h' }),
        [activity?.created_at],
    )

    // Memoize the full name to avoid string concatenation on every render
    const actorFullName = useMemo(
        () => activity?.actor.first_name + ' ' + activity?.actor.last_name,
        [activity?.actor.first_name, activity?.actor.last_name],
    )

    return (
        <div key={activity?.id} className="border-l relative py-4 flex">
            <div className="absolute flex items-center justify-center w-[30px] h-[30px] rounded-full bg-[#F8F6FE] border top-4 -left-4">
                <GetActivityIcon activity={activity} />
            </div>
            <div className="pl-6 ">
                <AssigneeAvatar assignee={actorFullName} imageUrl={activity?.actor.img_url || ''} className="w-[30px] h-[30px]" />
            </div>
            <div className="pl-2 w-full">
                <div className="flex items-start gap-3 mb-2">
                    <GetActivityTitle activity={activity} />
                </div>
                <div className="flex  items-center gap-2  pt-1">
                    <Calendar className="h-4 w-4" color="#5C5F62" />
                    <div className="text-xs text-[#5C5F62]">{formattedDate}</div>
                </div>
            </div>
        </div>
    )
})

ActivityItem.displayName = 'ActivityItem'

const ActivityLogContainer = ({ entityType, entityId }: ActivityLogContainerPropType) => {
    const { currentWorkspace } = useAuthStore()

    // Memoize query parameters to prevent unnecessary refetches
    const queryParams = useMemo(
        () => ({
            workspace_id: currentWorkspace?.id,
            entity_type: entityType,
            entity_id: entityId,
        }),
        [currentWorkspace?.id, entityType, entityId],
    )

    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, status } = useInfiniteQuery({
        queryKey: ['activityLog', queryParams],
        queryFn: async ({ pageParam = 1 }) => {
            const response = await api.get(endpoints.activityLog.listActivityLogs, {
                params: {
                    page: pageParam,
                    limit: 15,
                    ...queryParams,
                },
            })
            return response.data
        },
        getNextPageParam: (lastPage) => {
            const { currentPage, totalPages } = lastPage.pagination
            return currentPage < totalPages ? currentPage + 1 : undefined
        },
        initialPageParam: 1,
        enabled: !!currentWorkspace?.id,
        gcTime: 0,
        staleTime: 0,
    })

    const loadMoreRef = useRef<HTMLDivElement>(null)

    // Memoize the load more handler to prevent useEffect re-runs
    const handleLoadMore = useCallback(() => {
        if (hasNextPage && !isFetchingNextPage) {
            fetchNextPage()
        }
    }, [hasNextPage, isFetchingNextPage, fetchNextPage])

    // Memoize intersection observer options
    const observerOptions = useMemo(
        () => ({
            threshold: 0.1,
            rootMargin: '50px',
        }),
        [],
    )

    useEffect(() => {
        const observer = new IntersectionObserver((entries) => {
            const target = entries[0]
            if (target.isIntersecting) {
                handleLoadMore()
            }
        }, observerOptions)

        const currentRef = loadMoreRef.current
        if (currentRef) {
            observer.observe(currentRef)
        }

        return () => {
            if (currentRef) {
                observer.unobserve(currentRef)
            }
        }
    }, [handleLoadMore, observerOptions])

    // Memoize the flattened activities list to avoid recalculation
    const allActivities = useMemo(() => {
        return data?.pages.flatMap((page) => page.data || []) || []
    }, [data?.pages])

    if (status === 'pending') {
        return (
            <ScrollArea className="h-140">
                <div className="flex justify-center p-4">
                    <Loader size={20} className="animate-spin" />
                </div>
            </ScrollArea>
        )
    }
    if (allActivities.length === 0) {
        return (
            <div className="flex justify-center items-center h-50 p-4">
                <p className="text-sm text-gray-500">No activity found</p>
            </div>
        )
    }

    return (
        <ScrollArea className="h-80">
            <div className="flex flex-col  pl-8 pt-4">
                {allActivities.map((activity) => (
                    <ActivityItem key={activity.id} activity={activity} />
                ))}

                {/* Intersection Observer Target */}
                {hasNextPage && (
                    <div ref={loadMoreRef} className="flex justify-center p-4 min-h-[20px]">
                        {isFetchingNextPage ? (
                            <div className="text-sm text-gray-500">
                                <Loader size={16} className="animate-spin" />
                            </div>
                        ) : (
                            <div className="text-sm text-gray-400">
                                <ArrowBigDown size={16} className="animate-bounce" />
                            </div>
                        )}
                    </div>
                )}

                {!hasNextPage && allActivities.length > 0 && (
                    <div className="flex justify-center p-4">
                        <div className="text-sm text-gray-500">No more items to load</div>
                    </div>
                )}
            </div>
        </ScrollArea>
    )
}

export default React.memo(ActivityLogContainer)
