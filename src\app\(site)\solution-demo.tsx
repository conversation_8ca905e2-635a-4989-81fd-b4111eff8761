import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Image from 'next/image'
import NavigateToSignIn from './site-components/navigate-to-sign-in'

const SolutionDemo = () => {
    return (
        <section
            className="w-full max-w-5xl relative mx-auto  my-4 px-4 lg:px-0"
            aria-labelledby="solution-demo-heading"
            id="solution-demo">
            <div className="p-4 rounded-[32px] bg-[#0000000D]">
                <div className="block lg:flex gap-4">
                    <Card className="w-full lg:w-1/2 px-4">
                        <p className="text-[21px] md:text-[26px] 3xl:text-[32px] font-normal font-serif">Define Your Vision.</p>
                        <p className="text-[16px] font-medium">
                            Start a conversation with your AI co-pilot. It asks the right questions to understand your goals,
                            scope, and constraints, creating a detailed project brief in minutes.
                        </p>
                        <Image
                            src="/assets/img/chat-input.webp"
                            alt="Chat input interface for describing project goals in plain English"
                            width={1300}
                            height={800}
                            className="w-fill object-cover z-2 mt-4  lg:mt-16"
                            loading="lazy"
                            quality={85}
                            sizes="(max-width: 1024px) 100vw, 50vw"
                            style={{ height: 'auto' }}
                        />
                    </Card>
                    <Card className="w-full lg:w-1/2 px-4 mt-4 lg:mt-0">
                        <p className="text-[21px] md:text-[26px] 3xl:text-[32px] font-normal font-serif">
                            Generate Your Plan Instantly.
                        </p>
                        <p className="text-[16px] font-medium">
                            Based on your conversation, Raydian generates a comprehensive blueprint with every task, timeline, and
                            milestone already in place. Review and approve with a single click.
                        </p>
                        <Image
                            src="/assets/img/generated-task-item.webp"
                            alt="AI-generated task items showing automated project planning with tasks and timelines"
                            width={1300}
                            height={800}
                            className=" w-fill object-cover z-2"
                            loading="lazy"
                            quality={85}
                            sizes="(max-width: 1024px) 100vw, 50vw"
                            style={{ height: 'auto' }}
                        />
                    </Card>
                </div>
                <Card className="mt-4 py-0 overflow-hidden">
                    <div className="flex flex-col lg:flex-row justify-end lg:justify-between">
                        <div className="w-full lg:w-[45%]">
                            <div className="px-4 pt-6">
                                <p
                                    id="solution-demo-heading"
                                    className="text-[21px] md:text-[26px] 3xl:text-[32px] font-normal font-serif mb-6">
                                    Execute with Perfect Clarity.
                                </p>
                                <p className="text-[16px] font-medium max-w-xs pr-6">
                                    Publish the approved plan to a live, collaborative workspace. Assign tasks, track progress,
                                    and keep your entire team in sync from day one.
                                </p>
                                <NavigateToSignIn>
                                    <Button className="my-4" aria-label="Try Raydian project planning tool ">
                                        Get Started for Free.
                                    </Button>
                                </NavigateToSignIn>
                            </div>
                        </div>
                        <div className="hidden md:block md:w-[90%] md:mb-4 lg:mb-0 mx-auto md:border lg:border-none md:rounded-[24px] lg:rounded-none md:overflow-hidden lg:w-[55%]">
                            <Image
                                src="/assets/img/project-details.webp"
                                alt="Project dashboard showing team collaboration and task management interface"
                                width={1300}
                                height={800}
                                className=" w-fill object-cover lg:mt-10 z-2"
                                loading="lazy"
                                quality={90}
                                sizes="(max-width: 1024px) 100vw, 55vw"
                                style={{ height: 'auto' }}
                            />
                        </div>
                        <div className="flex md:hidden justify-end">
                            <div className="pb-2">
                                <Image
                                    src="/assets/img/project-details-mob.webp"
                                    alt="Project dashboard showing team collaboration and task management interface"
                                    width={1300}
                                    height={800}
                                    className=" w-fill object-contain z-2"
                                    loading="lazy"
                                    quality={90}
                                    sizes="(max-width: 1024px) 100vw, 55vw"
                                    style={{ height: '416px', width: 'auto' }}
                                />
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </section>
    )
}

export default SolutionDemo
