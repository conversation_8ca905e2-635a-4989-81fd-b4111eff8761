'use client'

import * as React from 'react'
import { BookOpen, BotMessageSquare, FolderKanban, Frame, Map, PieChart, Settings2, Users } from 'lucide-react'

import { NavMain } from '@/components/nav-main'
import { NavUser } from '@/components/nav-user'
import { TeamSwitcher } from '@/components/team-switcher'
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from '@/components/ui/sidebar'
import { useAuthStore } from '@/store/auth.store'
import FeedbackModal from './feedback-modal'
// This is sample data.
const staticData = {
    navMain: [
        {
            title: 'Chat',
            url: '/app/chat',
            icon: BotMessageSquare,
            isActive: true,
        },
        {
            title: 'Projects',
            url: '/app/projects',
            icon: BookOpen,
        },
        {
            title: 'Planner',
            url: '/app/planner',
            icon: FolderKanban,
        },
        // {
        //     title: 'Calendar',
        //     url: '/app/calendar',
        //     icon: CalendarDays,
        // },
        {
            title: 'Team Management',
            url: '/app/team-management',
            icon: Users,
        },
        {
            title: 'Settings',
            url: '/app/settings',
            icon: Settings2,
        },
    ],
    projects: [
        {
            name: 'Design Engineering',
            url: '#',
            icon: Frame,
        },
        {
            name: 'Sales & Marketing',
            url: '#',
            icon: PieChart,
        },
        {
            name: 'Travel',
            url: '#',
            icon: Map,
        },
    ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const user = useAuthStore((state) => state.user)

    const data = {
        ...staticData,
        user: {
            name: `${user?.first_name ?? ''} ${user?.last_name ?? ''}`,
            email: user?.email ?? '',
            avatar: user?.img_url || '',
        },
        workspaces: user?.workspaces || [],
    }

    return (
        <Sidebar collapsible="icon" variant="floating" {...props}>
            <SidebarHeader>
                <TeamSwitcher teams={data.workspaces} />
            </SidebarHeader>
            <SidebarContent className="mt-3">
                <NavMain items={data.navMain} />
                {/* <NavProjects projects={data.projects} /> */}
            </SidebarContent>
            <SidebarFooter>
                <FeedbackModal />
                <NavUser user={data.user} />
            </SidebarFooter>
            <SidebarRail />
        </Sidebar>
    )
}
