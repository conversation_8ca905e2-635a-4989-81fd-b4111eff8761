# ----------------------
# 📦 Build Stage
# ----------------------
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Accept build-time arguments
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_MINIO_ACCESS_KEY
ARG NEXT_PUBLIC_MINIO_SECRET_KEY
ARG NEXT_PUBLIC_MINIO_BUCKET_NAME
ARG NEXT_PUBLIC_MINIO_ENDPOINT
ARG NEXT_PUBLIC_MINIO_USE_SSL
ARG NEXT_PUBLIC_MINIO_PORT
ARG NEXT_PUBLIC_MINIO_PUBLIC_URL
ARG NEXT_PUBLIC_GA_MEASUREMENT_ID

# Inject env vars into .env.production
RUN echo "NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL" > .env.production && \
    echo "NEXT_PUBLIC_MINIO_ACCESS_KEY=$NEXT_PUBLIC_MINIO_ACCESS_KEY" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_SECRET_KEY=$NEXT_PUBLIC_MINIO_SECRET_KEY" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_BUCKET_NAME=$NEXT_PUBLIC_MINIO_BUCKET_NAME" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_ENDPOINT=$NEXT_PUBLIC_MINIO_ENDPOINT" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_USE_SSL=$NEXT_PUBLIC_MINIO_USE_SSL" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_PORT=$NEXT_PUBLIC_MINIO_PORT" >> .env.production && \
    echo "NEXT_PUBLIC_MINIO_PUBLIC_URL=$NEXT_PUBLIC_MINIO_PUBLIC_URL" >> .env.production \
    echo "NEXT_PUBLIC_GA_MEASUREMENT_ID=$NEXT_PUBLIC_GA_MEASUREMENT_ID" >> .env.production

# Build Next.js app
RUN npm run build

# ----------------------
# 🚀 Runtime Stage
# ----------------------
FROM node:22-alpine

WORKDIR /app

# Copy only the necessary built artifacts
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/.env.production ./.env.production

EXPOSE 3000

CMD ["npm", "start"]
