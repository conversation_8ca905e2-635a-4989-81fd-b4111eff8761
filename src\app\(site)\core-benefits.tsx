import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import Image from 'next/image'

const commonCardStyle = 'rounded-[24px] p-4 block'

const CoreBenefits = () => {
    return (
        <section
            className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-0 pb-0 lg:pb-35 relative rounded-[32px] overflow-hidden lg:border"
            aria-labelledby="core-benefits-heading"
            id="core-benefits">
            {/* Background Image */}
            <Image
                src="/assets/img/to-do-bg-image.webp"
                fill
                alt="Raydian AI project management dashboard showing task organization and workflow visualization"
                className="absolute inset-0 object-cover rounded-[32px] hidden lg:block"
                quality={75}
                sizes="(max-width: 1280px) 100vw, 1280px"
                loading="lazy"
                style={{ zIndex: 0 }}
            />
            <div className="w-full relative z-1">
                <div className="flex justify-center items-center mt-6 sm:mt-8 relative z-1">
                    <Badge className="bg-[#0000000D] text-[#000000] border border-[#E4E7EC] py-2 px-4 rounded-[8px]  text-xs md:text-sm font-medium">
                        CORE BENEFITS
                    </Badge>
                </div>
                <h2
                    id="core-benefits-heading"
                    className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-[64px] mx-auto font-serif font-medium text-center max-w-3xl text-[#18181B] leading-tight mt-6 sm:mt-8 mb-6">
                    More Than a To-Do List. A True Co-Pilot.
                </h2>
                <GridContainer />
            </div>
        </section>
    )
}

export default CoreBenefits

const TaskGenerationCard = () => {
    return (
        <Card className={cn(commonCardStyle)}>
            <h3 className="font-normal text-[26px] font-serif flex items-center gap-2 mb-5">Task Generation</h3>
            <p className="text-base xl:text-base font-normal xl:font-medium  mb-6">
                Go from a single sentence to a detailed project plan in seconds. Our AI understands your goals and builds the
                roadmap for you.
            </p>
            <Image
                src="/assets/img/chat-input.webp"
                alt="AI task generation interface showing how to input project goals and generate detailed plans"
                width={1300}
                height={800}
                className="w-full object-cover z-2"
                loading="lazy"
                quality={85}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 40vw"
                style={{ height: 'auto' }}
            />
        </Card>
    )
}

const RoadMapCard = () => {
    return (
        <Card className={cn(commonCardStyle, 'px-4 pb-0 pt-4')}>
            <h3 className="font-normal text-[26px] font-serif flex items-center gap-2 mb-5">Roadmap</h3>
            <p className="text-base font-normal xl:font-medium mb-4">
                Within seconds, see your ideas turn into reality. No more waiting for inspiration to strike.
            </p>
            <Image
                src="/assets/img/roadmap.webp"
                alt="Project roadmap visualization showing timeline and milestones for project planning"
                width={1300}
                height={800}
                className=" w-fill object-cover z-2 pt-4"
                loading="lazy"
                quality={80}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 60vw"
                style={{ height: 'auto' }}
            />
        </Card>
    )
}

const InsightsCard = () => {
    return (
        <Card className={cn(commonCardStyle, 'p-6')}>
            <h3 className="font-normal text-[26px] font-serif flex items-center gap-2 mb-5">Contextual Insights</h3>
            <p className="text-base font-normal xl:font-medium mb-8">
                Raydian monitors your progress and flags potential bottlenecks and risks, giving you the foresight to stay on
                course.
            </p>
            <Image
                src="/assets/img/insights.webp"
                alt="AI insights dashboard showing project analytics, bottlenecks detection, and risk assessment"
                width={1300}
                height={800}
                className=" w-fill object-cover z-2 pr-0 lg:pr-12"
                loading="lazy"
                quality={85}
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 50vw"
                style={{ height: 'auto' }}
            />
        </Card>
    )
}

const ClarityCard = () => {
    return (
        <Card className={cn(commonCardStyle, ' text-black p-0 overflow-hidden relative bg-transparent')}>
            <div className="p-6 relative z-1">
                <h3 className="font-normal text-[26px] font-serif flex items-center gap-2 mb-5">Clarity in every decision</h3>
                <p className="text-base font-normal xl:font-medium mb-8">
                    Start with up to 100 free prompts when you sign up. Explore and experiment without limits.
                </p>
            </div>
            <div className="absolute inset-0 flex items-center justify-center z-0">
                <div
                    className="absolute p-2 rounded-full backdrop-blur-2xl 
                bg-[radial-gradient(circle_at_center,_#2EB57199,_#96DAB866,_#FFFFFF33)] 
                blur-2xl h-94 w-94"
                />
            </div>

            <div className="flex justify-end">
                <Image
                    src="/assets/img/person.webp"
                    alt="Professional person using Raydian for project planning and decision making"
                    width={346}
                    height={346}
                    className=" w-full object-contain z-2 "
                    loading="lazy"
                    quality={85}
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 346px"
                    style={{ height: 'auto', width: 'auto' }}
                />
            </div>
        </Card>
    )
}

const CollaborateCard = () => {
    return (
        <Card className={cn(commonCardStyle)}>
            <div className="block lg:flex">
                <div className="w-full lg:max-w-[50%]">
                    <h3 className="font-normal text-[26px] font-serif flex items-center gap-2 mb-5">Collaborate</h3>
                    <p className="text-base font-normal xl:font-medium mb-4">
                        Raydian monitors your progress and flags potential bottlenecks and risks, giving you the foresight to stay
                        on course.
                    </p>
                </div>
                <div className="flex w-full justify-center">
                    <Image
                        src="/assets/img/collaboration.webp"
                        alt="Team collaboration interface showing shared project workspace and task assignment features"
                        width={1300}
                        height={800}
                        className=" w-fill object-contain z-2 t-4"
                        loading="lazy"
                        quality={80}
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 478px"
                        style={{ height: 'auto', width: '478px' }}
                    />
                </div>
            </div>
        </Card>
    )
}

const GridContainer = () => {
    return (
        <div className="w-full space-y-2 bg-[#0000000D] p-2 rounded-[32px]">
            <div className="grid justify-between grid-cols-1 md:grid-cols-[49%_50%] lg:grid-cols-[39%_60%] gap-2 md:gap-0">
                <TaskGenerationCard />
                <RoadMapCard />
            </div>

            <div className="grid justify-between grid-cols-1 md:grid-cols-[49%_50%] gap-2 md:gap-0">
                <InsightsCard />
                <ClarityCard />
            </div>
            <div className="grid grid-cols-1">
                <CollaborateCard />
            </div>
        </div>
    )
}
