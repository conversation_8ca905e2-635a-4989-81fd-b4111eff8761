import React, { JSX, useMemo } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ig, MessageCircleMore, Puzzle, Rocket, User<PERSON>he<PERSON>, User<PERSON><PERSON> } from 'lucide-react'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'
import { ActivityLogType, AcceptanceCriteriaItem, actor } from '@/types/activity-log'
import { formatDate } from '@/utils/format-date.utils'
import LogTaskChip from './log-task-chip'

interface SimpleChange {
    old: string
    new: string
}

interface ArrayChange {
    old: AcceptanceCriteriaItem[]
    new: AcceptanceCriteriaItem[]
}

type Change = SimpleChange | ArrayChange

/* ------------------------------
   Constants (hoisted)
------------------------------- */
const FIELD_LABELS: Record<string, string> = {
    priority_id: 'Priority',
    status_id: 'Status',
    due_date: 'Due Date',
    start_date: 'Start Date',
    acceptance_criteria: 'Acceptance Criteria',
} as const

const ICON_STYLE = { color: '#08B38B', className: 'h-3.5 w-3.5' } as const

const ICON_MAP = {
    comment: <MessageCircleMore color={ICON_STYLE.color} className={ICON_STYLE.className} />,
    assigneeAdded: <UserCheck color={ICON_STYLE.color} className={ICON_STYLE.className} />,
    assigneeRemoved: <UserMinus color={ICON_STYLE.color} className={ICON_STYLE.className} />,
    feature: <Puzzle color={ICON_STYLE.color} className={ICON_STYLE.className} />,
    default: <CircleCheckBig color={ICON_STYLE.color} className={ICON_STYLE.className} />,
    ai: <Rocket color={ICON_STYLE.color} className={ICON_STYLE.className} />,
} as const

/* ------------------------------
   RenderAcceptanceCriteria
------------------------------- */
export const RenderAcceptanceCriteria = React.memo(
    ({ acceptanceCriteria }: { acceptanceCriteria?: { old: AcceptanceCriteriaItem[]; new: AcceptanceCriteriaItem[] } }) => {
        const changesList = useMemo(() => {
            if (!acceptanceCriteria?.old || !acceptanceCriteria?.new) return []

            const oldMap = new Map(acceptanceCriteria.old.map((o) => [o.id, o]))
            return acceptanceCriteria.new.reduce<JSX.Element[]>((acc, newItem) => {
                const oldItem = oldMap.get(newItem.id)
                if (oldItem && oldItem.is_checked !== newItem.is_checked) {
                    acc.push(
                        <div className="my-2" key={newItem.id}>
                            <span className="text-gray-600 font-semibold">Criteria :</span> &quot;{newItem.criteria}&quot;{' '}
                            <span className="text-[#18181B]">
                                <span className="text-gray-400">→</span>
                                {newItem.is_checked ? ' Marked as completed' : ' Marked as not completed'}
                            </span>
                        </div>,
                    )
                }
                return acc
            }, [])
        }, [acceptanceCriteria])

        if (changesList.length === 0) return null

        return (
            <span>
                {changesList.map((msg, index) => (
                    <span key={index}>{msg}</span>
                ))}
            </span>
        )
    },
)

RenderAcceptanceCriteria.displayName = 'RenderAcceptanceCriteria'

/* ------------------------------
   GetActivityIcon
------------------------------- */
export const GetActivityIcon = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const iconKey = useMemo(() => {
        const logTitle = activity?.logTitle?.toLowerCase() ?? ''

        if (logTitle.includes('comment')) return 'comment'
        if (logTitle.includes('assignee added')) return 'assigneeAdded'
        if (logTitle.includes('assignee removed')) return 'assigneeRemoved'
        if (logTitle.includes('feature')) return 'feature'
        if (logTitle.includes('suggested by ai')) return 'ai'
        return 'default'
    }, [activity?.logTitle])

    return ICON_MAP[iconKey as keyof typeof ICON_MAP]
})

GetActivityIcon.displayName = 'GetActivityIcon'

/* ------------------------------
   Helper Functions
------------------------------- */
const renderChange = (field: string, change: Change): JSX.Element => {
    const isSimpleValue = !Array.isArray(change.old) && !Array.isArray(change.new)
    const fieldLabel = FIELD_LABELS[field] || field.replace(/_/g, ' ')

    let content: JSX.Element

    switch (field) {
        case 'bug':
            content = <span>{change.new ? 'marked as bug' : 'no longer a bug'}</span>
            break
        case 'overview':
            content = <span>&apos;s overview</span>
            break
        case 'description':
            content = (
                <>
                    <span>Updated Description</span>
                    <div className="px-2 py-0.5 text-gray-700 ">From : {String(change?.old)}</div>
                    <div className="px-2 py-0.5 text-gray-700 ">To : {String(change?.new)}</div>
                </>
            )
            break
        default:
            content = (
                <div className="my-2 items-center gap-1 text-xs">
                    <span className="inline-flex items-center gap-1 text-xs">
                        <span className="text-gray-600 font-semibold">{fieldLabel}</span>
                        <span className="text-gray-400">from</span>
                        <span className="px-2 py-0.5 bg-gray-50 border border-gray-300 text-gray-700 rounded-full font-medium">
                            {String(change?.old)}
                        </span>
                        <span className="text-gray-400">→</span>
                        <span className="px-2 py-0.5 bg-green-50 border border-green-300 text-green-700 rounded-full font-medium">
                            {String(change?.new)}
                        </span>
                    </span>
                </div>
            )
    }

    return (
        <span key={field} className="font-medium text-[13px] text-[#18181B]">
            {isSimpleValue ? (
                content
            ) : (
                <RenderAcceptanceCriteria
                    acceptanceCriteria={change as { old: AcceptanceCriteriaItem[]; new: AcceptanceCriteriaItem[] }}
                />
            )}
        </span>
    )
}

const getActorName = (actor: actor): string => {
    return `${actor?.first_name ?? ''} ${actor?.last_name ?? ''}`.trim()
}

/* ------------------------------
   AssigneeItem Component
------------------------------- */
const AssigneeItem = React.memo(({ user, showComma }: { user: actor; showComma: boolean }) => {
    const fullName = useMemo(() => `${user.first_name} ${user.last_name}`, [user.first_name, user.last_name])

    return (
        <span className="flex items-center gap-1">
            <AssigneeAvatar assignee={fullName} imageUrl={user.img_url || ''} showTooltip={false} />
            <span>
                {fullName}
                {showComma && ','}
            </span>
        </span>
    )
})

AssigneeItem.displayName = 'AssigneeItem'

/* ------------------------------
   GetActivityTitle
------------------------------- */
export const GetActivityTitle = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const normalizedLogTitle = useMemo(() => {
        const logTitle = activity.logTitle
        const lower = logTitle?.toLowerCase()

        if (lower?.includes('comment') || lower?.includes('comments')) return 'comment'
        if (lower?.includes('feature')) return 'feature'
        return logTitle
    }, [activity.logTitle])

    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const renderDefault = () => {
        const { action, entityType, entityData, changes } = activity
        const entityTitle = entityData?.title ?? ''
        const hasChanges = changes && Object.keys(changes).length > 0

        return (
            <span className="font-medium text-[13px] text-[#334155]">
                <span>{actorName}</span> <span className="font-bold">{action}</span>{' '}
                <span>{entityType === 'project' ? 'this project' : entityType}</span>
                {entityTitle && <span> &quot;{entityTitle}&quot;</span>}
                {hasChanges && <span>{Object.entries(changes).map(([field, change]) => renderChange(field, change))}</span>}
            </span>
        )
    }

    switch (normalizedLogTitle) {
        case 'Assignee added to task':
        case 'Assignee removed from task':
            return <GetAssigneeChangeText activity={activity} />
        case 'comment':
            return <GetCommentText activity={activity} />
        case 'Dependency added to task':
            return <GetDependencyChangeText activity={activity} />
        case 'feature':
            return <GetFeatureChangeText activity={activity} />
        case 'Assignee added to project':
        case 'Assignee removed from project':
            return <GetProjectAssigneeChangeText activity={activity} />
        case 'Tasks updated':
            return <GetTaskBulkUpdateText activity={activity} />
        case 'Published tasks suggested by Ai':
            return <GetAiTaskCreationText activity={activity} />
        case 'Tasks deleted':
            return <GetTaskBulkDeleteText activity={activity} />
        default:
            return renderDefault()
    }
})

GetActivityTitle.displayName = 'GetActivityTitle'

/* ------------------------------
   Specific Activity Components
------------------------------- */
const GetAssigneeChangeText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const { assignees, taskTitles, isAdded, isRemoved, taskDetails } = useMemo(() => {
        const assignees = activity.entityData?.details?.userDetails ?? []
        const taskDetails = activity.entityData?.details?.taskDetails ?? []
        const taskTitles = taskDetails.map((task) => task.short_code)

        const isAdded = activity.logTitle?.toLowerCase().includes('added')
        const isRemoved = activity.logTitle?.toLowerCase().includes('removed')

        return { assignees, taskTitles, isAdded, isRemoved, taskDetails }
    }, [activity.entityData, activity.logTitle])

    return (
        <span className="font-medium text-[13px] flex items-center flex-wrap gap-1 text-[#334155]">
            <span>{actorName}</span>
            {isAdded && <span className="font-bold">{taskDetails.length > 1 ? 'assigned tasks' : ' assigned task'}</span>}
            {isRemoved && <span className="font-bold">unassigned task</span>}

            <span className="flex items-center gap-2">
                {taskTitles.map((taskTitle, index) => (
                    <LogTaskChip key={index}>{taskTitle}</LogTaskChip>
                ))}
            </span>
            <span className="ml-1">{isAdded ? 'to' : 'from'}</span>
            <span className="flex items-center gap-2">
                {assignees.map((user, index) => (
                    <AssigneeItem key={user.id} user={user} showComma={index < assignees.length - 1} />
                ))}
            </span>
        </span>
    )
})

GetAssigneeChangeText.displayName = 'GetAssigneeChangeText'

const GetCommentText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const commentData = useMemo(() => {
        const logTitle = activity.logTitle
        const rawTitle = activity.entityData?.title ?? ''
        const entityType = activity.entityType

        let taskTitle = ''
        let commentText = ''
        let originalComment = ''
        let projectTitle = ''

        const parseCommentData = () => {
            const splitIndex = rawTitle.indexOf(':')
            const beforeColon = splitIndex !== -1 ? rawTitle.substring(0, splitIndex).trim() : rawTitle
            const afterColon = splitIndex !== -1 ? rawTitle.substring(splitIndex + 1).trim() : ''

            return { beforeColon, afterColon }
        }

        const { beforeColon, afterColon } = parseCommentData()

        switch (logTitle) {
            case 'Comment created':
                if (entityType === 'task') {
                    if (/^Reply for comment/i.test(rawTitle)) {
                        commentText = afterColon
                        const match = beforeColon.match(/^Reply for comment (.*) on task\s*(.*)$/i)
                        if (match) {
                            originalComment = match[1]?.trim()
                            taskTitle = match[2]?.trim()
                        }
                    } else {
                        taskTitle = beforeColon.replace(/^New comment on task\s*/i, '').trim()
                        commentText = afterColon
                    }
                } else if (entityType === 'project') {
                    if (/^Reply for comment/i.test(rawTitle)) {
                        commentText = afterColon
                        const match = beforeColon.match(/^Reply for comment (.*) on project\s*(.*)$/i)
                        if (match) {
                            originalComment = match[1]?.trim()
                        }
                    } else {
                        projectTitle = beforeColon.replace(/^New comment on project\s*/i, '').trim()
                        commentText = afterColon
                    }
                }
                break

            case 'Comment upvoted':
            case 'Comment upvote removed':
                if (/^Upvoted reply for/i.test(rawTitle) || /^Removed upvote from reply/i.test(rawTitle)) {
                    commentText = afterColon
                } else {
                    commentText = afterColon || rawTitle
                }
                break

            case 'Comment deleted':
                if (entityType === 'task') {
                    if (/^Removed reply for comment/i.test(rawTitle)) {
                        taskTitle = beforeColon.replace(/^Removed reply for comment\s*/i, '').trim()
                        commentText = afterColon
                    } else {
                        taskTitle = beforeColon.replace(/^Removed comment on task\s*/i, '').trim()
                        commentText = afterColon
                    }
                } else if (entityType === 'project') {
                    if (/^Removed reply for comment/i.test(rawTitle)) {
                        commentText = afterColon
                        const match = beforeColon.match(/^Removed reply for comment (.*) on project/i)
                        if (match) {
                            originalComment = match[1]?.trim()
                        }
                    } else {
                        commentText = afterColon
                    }
                }
                break
            case 'Comment updated':
                if (entityType === 'task') {
                    taskTitle = activity.entityData.short_code

                    const content = activity?.changes?.content
                    commentText = ` from ${content?.old} to ${content?.new}`
                } else if (entityType === 'project') {
                    projectTitle = ''
                    commentText = ` from ${activity?.changes?.content?.old} to ${activity?.changes?.content?.new}`
                }
                break
        }

        return {
            logTitle,
            taskTitle,
            commentText,
            originalComment,
            projectTitle,
            entityType,
            isUpvoteRemoved: logTitle === 'Comment upvote removed',
            rawTitle,
        }
    }, [activity.logTitle, activity.entityData, activity.entityType, activity?.changes?.content])

    const renderCommentContent = () => {
        const { logTitle, taskTitle, commentText, originalComment, projectTitle, entityType, isUpvoteRemoved } = commentData

        switch (logTitle) {
            case 'Comment created':
                if (entityType === 'task') {
                    if (originalComment) {
                        return (
                            <>
                                <span className="font-bold">replied to comment</span>
                                {originalComment && <span className="text-[#18181B]">&quot;{originalComment}&quot;</span>}
                                {taskTitle && (
                                    <>
                                        <span>on task</span> <span>{taskTitle}</span>
                                    </>
                                )}
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                            </>
                        )
                    } else {
                        return (
                            <>
                                <span className="font-bold">commented on task</span>
                                <LogTaskChip>{taskTitle}</LogTaskChip>
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                            </>
                        )
                    }
                } else if (entityType === 'project') {
                    if (originalComment) {
                        return (
                            <>
                                <span className="font-bold">replied to comment</span>
                                {originalComment && <span className="text-[#18181B]">&quot;{originalComment}&quot;</span>}
                                {commentText && <span className="text-[#18181B]">with &quot;{commentText}&quot;</span>}
                                <span>on this project</span>
                            </>
                        )
                    } else {
                        return (
                            <>
                                <span className="font-bold">commented on this project</span>
                                {projectTitle && <span>&quot;{projectTitle}&quot;</span>}
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                            </>
                        )
                    }
                }
                break

            case 'Comment upvoted':
            case 'Comment upvote removed':
                const isReply =
                    /^Upvoted reply for/i.test(commentData.rawTitle) || /^Removed upvote from reply/i.test(commentData.rawTitle)

                return (
                    <>
                        <span className="font-bold">
                            {isUpvoteRemoved
                                ? isReply
                                    ? 'removed an upvote from a reply'
                                    : 'removed an upvote from a comment'
                                : isReply
                                  ? 'upvoted a reply'
                                  : 'upvoted a comment'}
                        </span>
                        {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                        {entityType === 'project' && <span className="font-semibold">on this project</span>}
                    </>
                )

            case 'Comment deleted':
                if (entityType === 'task') {
                    if (originalComment) {
                        return (
                            <>
                                <span className="font-bold text-[#FB3748]">deleted</span>
                                <span className="font-semibold"> a reply</span>
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                                {taskTitle && (
                                    <>
                                        <span>of comment</span> <span>on task</span> <LogTaskChip>{taskTitle}</LogTaskChip>
                                    </>
                                )}
                            </>
                        )
                    } else {
                        return (
                            <>
                                <span className="font-bold text-[#FB3748]">deleted</span>
                                <span className="font-semibold"> a comment</span>
                                <span>on task</span>
                                <LogTaskChip>{taskTitle}</LogTaskChip>
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                            </>
                        )
                    }
                } else if (entityType === 'project') {
                    if (originalComment) {
                        return (
                            <>
                                <span className="font-bold text-[#FB3748]">deleted</span>
                                <span className="font-semibold"> a reply</span>
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                                {originalComment && (
                                    <>
                                        <span>of comment</span>
                                        <span className="text-[#18181B]">&quot;{originalComment}&quot;</span>
                                    </>
                                )}
                                <span>on this project</span>
                            </>
                        )
                    } else {
                        return (
                            <>
                                <span className="font-bold text-[#FB3748]">deleted</span>
                                <span className="font-semibold"> a comment</span>
                                {commentText && <span className="text-[#18181B]">&quot;{commentText}&quot;</span>}
                                <span>on this project</span>
                            </>
                        )
                    }
                }
                break
            case 'Comment updated':
                if (entityType === 'task') {
                    return (
                        <>
                            <span className="font-bold">edited comment on task</span>
                            <LogTaskChip>{taskTitle}</LogTaskChip>
                            <span className="text-[#18181B]">&quot;{commentText}&quot;</span>
                        </>
                    )
                } else if (entityType === 'project') {
                    return (
                        <>
                            <span className="font-bold">edited a comment on this project</span>
                            <span className="text-[#18181B]">&quot;{commentText}&quot;</span>
                        </>
                    )
                }
                break
        }

        return (
            <>
                did something on <span>&quot;{taskTitle || 'unknown'}&quot;</span>
            </>
        )
    }

    return (
        <span className="font-medium text-[13px] flex items-center flex-wrap gap-1 text-[#334155]">
            <span>{actorName}</span>
            {renderCommentContent()}
        </span>
    )
})

GetCommentText.displayName = 'GetCommentText'

const GetDependencyChangeText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const dependencyData = useMemo(() => {
        const rawTitle = activity.entityData?.title ?? ''
        const logTitle = activity.logTitle
        const taskTitle = activity.entityData?.details?.title ?? ''
        const shortCode = activity.entityData?.details?.short_code ?? ''

        let actionWord = ''
        let dependencyText = ''

        switch (logTitle) {
            case 'Dependency added to task':
                actionWord = 'added'
                dependencyText = rawTitle.replace(/^Added\s*/i, '').trim()
                break
            case 'Dependency removed from task':
                actionWord = 'removed'
                dependencyText = rawTitle.replace(/^Removed\s*/i, '').trim()
                break
            default:
                dependencyText = rawTitle
                break
        }

        return { actionWord, dependencyText, taskTitle, shortCode }
    }, [activity.entityData, activity.logTitle])

    return (
        <span className="font-medium text-[13px] flex items-center flex-wrap gap-1 text-[#334155]">
            <span>{actorName}</span>
            <span className="font-bold">{dependencyData.actionWord}</span>
            <span>{dependencyData.dependencyText}</span>
            {dependencyData.taskTitle && (
                <span>
                    &quot;{dependencyData.taskTitle}&quot; ({dependencyData.shortCode})
                </span>
            )}
        </span>
    )
})

GetDependencyChangeText.displayName = 'GetDependencyChangeText'

const GetFeatureChangeText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const featureData = useMemo(() => {
        const featureTitle = activity.entityData?.title || 'Untitled feature'
        const featureCode = activity.entityData?.feat_code ? `(${activity.entityData.feat_code})` : ''
        const isCreated = activity.logTitle?.toLowerCase().includes('created')
        const isDeleted = activity.logTitle?.toLowerCase().includes('deleted')

        return { featureTitle, featureCode, isCreated, isDeleted }
    }, [activity.entityData, activity.logTitle])

    return (
        <span className="font-medium text-[13px] flex items-center flex-wrap gap-1 text-[#334155]">
            <span>{actorName}</span>
            {featureData.isCreated && <span className="font-bold">created feature</span>}
            {featureData.isDeleted && <span className="font-bold">deleted feature</span>}
            <span>
                &quot;{featureData.featureTitle}&quot; {featureData.featureCode}
            </span>
        </span>
    )
})

GetFeatureChangeText.displayName = 'GetFeatureChangeText'

const GetProjectAssigneeChangeText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const { assignees, isAdded, isRemoved } = useMemo(() => {
        const assignees = activity.entityData?.details?.userDetails ?? []
        const isAdded = activity.logTitle?.toLowerCase().includes('added')
        const isRemoved = activity.logTitle?.toLowerCase().includes('removed')

        return { assignees, isAdded, isRemoved }
    }, [activity.entityData, activity.logTitle])

    return (
        <span className="font-medium text-[13px] flex items-center flex-wrap gap-1 text-[#334155]">
            <span>{actorName}</span>
            {isAdded && <span className="font-bold">added assignee(s) to project</span>}
            {isRemoved && <span className="font-bold">removed assignee(s) from project</span>}
            <span className="flex items-center gap-2">
                {assignees.map((user, index) => (
                    <AssigneeItem key={user.id} user={user} showComma={index < assignees.length - 1} />
                ))}
            </span>
        </span>
    )
})

GetProjectAssigneeChangeText.displayName = 'GetProjectAssigneeChangeText'

const GetTaskBulkUpdateText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const updatedTasks = useMemo(() => {
        return activity.entityData?.updatedTasks ?? []
    }, [activity.entityData])
    return (
        <div className="font-medium text-[13px] text-[#334155] w-full">
            <div className="flex items-start pt-[3px] mb-1 w-full">
                <span>{actorName}</span>
                <p className="font-bold pl-1 w-fit">
                    {' '}
                    {updatedTasks.length > 0 ? 'updated the tasks' : activity.entityData?.title}
                </p>
                <div className="flex gap-1 pl-1 w-full sm:max-w-[60%] flex-wrap">
                    {updatedTasks.length > 0 &&
                        updatedTasks.map((task) => <LogTaskChip key={task.id}>{task.short_code}</LogTaskChip>)}
                </div>
            </div>

            <div className="space-y-3 text-xs my-3">
                {Object.entries(activity.entityData?.updatedAtributes || {}).map(([key, value]) => {
                    // skip updated_at field
                    if (key === 'updated_at') return null
                    // format date fields
                    if (key === 'due_date') {
                        value = formatDate(new Date(value), 'DD MMM YYYY')
                    }
                    const labelMap = {
                        priority_id: 'Priority',
                        status_id: 'Status',
                        due_date: 'Due Date',
                    }

                    // make the key human readable
                    const label = labelMap[key as keyof typeof labelMap] || key.replace(/_/g, ' ')

                    return (
                        <div key={key}>
                            <span className="font-medium">
                                {label} <span className="text-gray-400"> to </span>{' '}
                            </span>
                            <span className="px-2 py-0.5 bg-green-50 border border-green-300 text-green-700 rounded-full font-medium">
                                {value}
                            </span>
                        </div>
                    )
                })}
            </div>
        </div>
    )
})
GetTaskBulkUpdateText.displayName = 'GetTaskBulkUpdateText'

const GetTaskBulkDeleteText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    const updatedTasks = useMemo(() => {
        return activity.entityData?.details?.taskDetails ?? []
    }, [activity.entityData])
    return (
        <div className="font-medium text-[13px] text-[#334155] w-full">
            <div className="flex items-start pt-[3px] mb-1 w-full">
                <span>{actorName}</span>
                <p className="font-bold pl-1 w-fit">
                    {' '}
                    {updatedTasks.length > 0 ? 'deleted the tasks' : activity.entityData?.title}
                </p>
                <div className="flex gap-1 pl-1 w-full sm:max-w-[60%] flex-wrap">
                    {updatedTasks.length > 0 &&
                        updatedTasks.map((task) => <LogTaskChip key={task.id}>{task.short_code}</LogTaskChip>)}
                </div>
            </div>
        </div>
    )
})

GetTaskBulkDeleteText.displayName = 'GetTaskBulkUpdateText'

const GetAiTaskCreationText = React.memo(({ activity }: { activity: ActivityLogType }) => {
    // const actorName = useMemo(() => getActorName(activity.actor), [activity.actor])

    return (
        <span className="font-medium text-[13px] text-[#334155]">
            Raydian AI
            <span className="font-bold"> generated </span>
            and
            <span className="font-bold"> published </span>
            {Array.isArray(activity.entityData?.details) ? activity.entityData.details.length : 1} tasks for this project
        </span>
    )
})

GetAiTaskCreationText.displayName = 'GetAiTaskCreationText'
