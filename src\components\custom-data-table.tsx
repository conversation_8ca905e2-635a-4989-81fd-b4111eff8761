'use client'

import { useEffect, useState, useCallback, useMemo, memo, JSX } from 'react'
import { ColumnDef, flexRender, getCoreRowModel, useReactTable, RowSelectionState } from '@tanstack/react-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from './ui/button'
import { cn } from '@/lib/utils'

interface DataTableProps<TData extends { id: string }, TValue> {
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
    headerCellClassName?: string
    tableContainerClassName?: string
    handlePreviousPage?: () => void
    handleNextPage?: () => void
    paginationDetails?: {
        currentPage: number
        totalPages: number
        total: number
        pageSize: number
    }
    customPaginationActions?: React.ReactNode
    onRowClick?: (row: TData) => void
    onSelectionChange?: (rows: TData[]) => void
    clearSelection?: boolean
}

export function DataTableComponent<TData extends { id: string }, TValue>({
    columns,
    data,
    headerCellClassName = 'bg-white text-[#71717A] text-sm max-w-[230px] pl-4',
    tableContainerClassName = 'rounded-sm border overflow-hidden shadow-sm shadow-gray-200 flex flex-col',
    handlePreviousPage,
    handleNextPage,
    paginationDetails,
    customPaginationActions,
    onRowClick,
    onSelectionChange,
    clearSelection = false,
}: DataTableProps<TData, TValue>) {
    const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

    // Memoize items on current page calculation
    const itemsOnCurrentPage = useMemo(
        () =>
            getItemsOnCurrentPage(
                paginationDetails?.total || 0,
                paginationDetails?.currentPage || 1,
                paginationDetails?.pageSize || 10,
            ),
        [paginationDetails?.total, paginationDetails?.currentPage, paginationDetails?.pageSize],
    )

    // Memoize table configuration
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        state: {
            rowSelection,
        },
        onRowSelectionChange: setRowSelection,
        enableRowSelection: true,
        getRowId: useCallback((row: TData) => row.id, []),
    })

    // Memoize selected rows calculation
    const selectedRowModel = table.getSelectedRowModel()
    const selectedRows = useMemo(() => selectedRowModel.flatRows.map((row) => row.original), [selectedRowModel])

    const selectedRowCount = useMemo(() => Object.keys(rowSelection).length, [rowSelection])

    // Memoize row click handler
    const handleRowClick = useCallback(
        (e: React.MouseEvent, rowData: TData) => {
            const element = e.target as HTMLElement
            if (onRowClick && element.id !== 'select') {
                onRowClick(rowData)
            }
        },
        [onRowClick],
    )

    // Effect for selection change - fix dependency array
    useEffect(() => {
        onSelectionChange?.(selectedRows)
    }, [onSelectionChange, selectedRows]) // Include selectedRows in dependencies

    // Clear selection when clearSelection is true
    useEffect(() => {
        if (clearSelection) {
            setRowSelection({})
        }
    }, [clearSelection])

    // Memoize pagination buttons to prevent unnecessary re-renders
    const paginationButtons = useMemo(
        () => (
            <div className="flex justify-center gap-4">
                <Button
                    onClick={handlePreviousPage}
                    disabled={paginationDetails?.currentPage === 1}
                    className="bg-white text-[#09090B] hover:bg-gray-300 rounded-sm">
                    Previous
                </Button>
                <Button
                    onClick={handleNextPage}
                    disabled={paginationDetails?.currentPage === paginationDetails?.totalPages}
                    className="bg-white text-[#09090B] hover:bg-gray-300 rounded-sm">
                    Next
                </Button>
            </div>
        ),
        [handlePreviousPage, handleNextPage, paginationDetails?.currentPage, paginationDetails?.totalPages],
    )

    return (
        <>
            <div
                className={cn(
                    tableContainerClassName,
                    selectedRowCount > 0 ? 'max-h-[calc(100dvh-300px)]' : 'max-h-[calc(100dvh-200px)]',
                )}>
                <Table>
                    <TableHeader className="sticky top-0 z-10 bg-white">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} className={headerCellClassName}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>

                    <TableBody className="bg-white w-fit">
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && 'selected'}
                                    onClick={(e) => handleRowClick(e, row.original)}
                                    className={onRowClick ? 'cursor-pointer hover:bg-gray-100 border-none' : 'border-none'}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id} className="border-none max-w-[230px] mx-7 px-4 overflow-clip">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {customPaginationActions && customPaginationActions}

            {!customPaginationActions && paginationDetails && (
                <div className="flex justify-between gap-4 my-2">
                    <div className="text-[#71717A] text-sm">
                        {selectedRowCount} of {itemsOnCurrentPage} row(s) selected
                    </div>
                    {paginationButtons}
                </div>
            )}
        </>
    )
}

// Memoize the entire component to prevent unnecessary re-renders when props haven't changed
export const DataTable = memo(DataTableComponent) as <TData extends { id: string }, TValue>(
    props: DataTableProps<TData, TValue>,
) => JSX.Element

// Move helper function outside component to prevent recreation
function getItemsOnCurrentPage(total: number, currentPage: number, pageSize: number): number {
    if (currentPage < 1 || pageSize <= 0 || total < 0) return 0

    const fullPagesItemCount = (currentPage - 1) * pageSize
    const remainingItems = total - fullPagesItemCount

    return remainingItems >= pageSize ? pageSize : Math.max(remainingItems, 0)
}
