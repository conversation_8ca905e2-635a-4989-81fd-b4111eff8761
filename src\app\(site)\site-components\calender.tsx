'use client'

import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import spotify from '../../../../public/assets/icons/spotify.svg'
import notion from '../../../../public/assets/icons/notion.svg'
import msLogo from '../../../../public/assets/img/MsLogo.svg'

import Image from 'next/image'

// Sample data for calendar events
const calendarEvents = {
    2: {
        type: 'subscription',
        icon: <Image src={notion} width={20} height={20} alt="Notion" />,
        color: '',
        tooltip: {
            title: 'Notion HQ',
            amount: '€32.00',
            description: 'Every month on 2nd',
            nextAction: 'Next payment',
            total: '€456.60',
            since: '06/2024',
        },
        showTooltip: true,
        showIcon: false,
    },
    8: {
        type: 'task',
        icon: <div className="w-[18px] h-[17px] rounded-full bg-[#283544]" />,
        color: 'bg-slate-800',
        tooltip: {
            title: '',
            amount: '',
            description: '',
            nextAction: '',
            total: '',
            since: '',
        },
        showTooltip: false,
        showIcon: true,
    },
    12: {
        type: 'subscription',
        icon: <div className="w-[20px] h-[20px] rounded-sm bg-[#FFD02F]" />,
        color: '',
        tooltip: {
            title: 'Miro',
            amount: '€15.00',
            description: 'Every month on 12th',
            nextAction: 'Next payment',
            total: '€250.60',
            since: '05/2022',
        },
        showTooltip: true,
        showIcon: true,
    },
    16: {
        type: 'subscription',
        icon: <Image src={msLogo} width={20} height={20} alt="Spotify" />,
        color: '',
        tooltip: {
            title: 'Microsoft',
            amount: '€15.00',
            description: 'Every month on 16th',
            nextAction: 'Next payment',
            total: '€250.60',
            since: '05/2022',
        },
        showTooltip: true,
        showIcon: false,
    },
    25: {
        type: 'subscription',
        icon: <Image src={spotify} width={20} height={20} alt="Spotify" />,
        color: '',
        tooltip: {
            title: 'Spotify',
            amount: '€15.00',
            description: 'Every month on 25th',
            nextAction: 'Next payment',
            total: '€250.60',
            since: '05/2022',
        },
        showTooltip: true,
        showIcon: true,
    },
    28: {
        type: 'subscription',
        icon: <Image src={notion} width={20} height={20} alt="Notion" />,
        color: '',
        tooltip: {
            title: 'Notion HQ',
            amount: '€32.00',
            description: 'Every month on 28th',
            nextAction: 'Next payment',
            total: '€456.60',
            since: '05/2022',
        },
        showTooltip: true,
        showIcon: false,
    },
}

export default function CalendarWithTooltip() {
    const [currentDate] = useState(new Date(2024, 9)) // October 2024

    const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ]

    const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']

    const getDaysInMonth = () => {
        const days = []

        // Add days of the month
        for (let day = 1; day <= 30; day++) {
            days.push(day)
        }

        // Add days from next month to fill the grid
        const remainingCells = 35 - days.length // 6 rows × 7 days
        for (let day = 1; day <= remainingCells; day++) {
            days.push(day)
        }

        return days
    }

    const days = getDaysInMonth()
    const currentMonth = monthNames[currentDate.getMonth()]
    const currentYear = currentDate.getFullYear()

    const totalTasks = Object.keys(calendarEvents).length - 3

    return (
        <TooltipProvider delayDuration={0}>
            <div className="max-w-2xl mx-auto px- ">
                <div className=" rounded-2xl px-8">
                    {/* Header */}
                    <div className="flex items-end justify-between mb-8 ">
                        <div className="flex items-center gap-4">
                            <h1 className="text-[20px]  text-gray-900">
                                {currentMonth} <span className="text-gray-400">{currentYear}</span>
                            </h1>
                        </div>
                        <div className="text-left">
                            <div className="text-[20px] text-gray-500">Tasks</div>
                            <div className="text-2xl font-semibold text-gray-900">{totalTasks}</div>
                        </div>
                    </div>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1">
                        {/* Day headers */}
                        {dayNames.map((day) => (
                            <div
                                key={day}
                                className="px-3 py-1 rounded-full bg-[#F5F5F5] text-center text-xs font-medium text-black">
                                {day}
                            </div>
                        ))}

                        {/* Calendar days */}
                        {days.map((day, index) => {
                            const isCurrentMonth = index < 30
                            const event = day && isCurrentMonth ? calendarEvents[day as keyof typeof calendarEvents] : null

                            return (
                                <Tooltip key={index}>
                                    <TooltipTrigger asChild className={cn('cursor-pointer ', event && 'hover:border')}>
                                        <div
                                            className={`relative group p-3 text-center text-sm min-h-[48px] flex flex-col items-center rounded-[8px] justify-center 
                                                bg-[#F5F5F5] ,text-gray-900
                                                `}>
                                            {day && (
                                                <>
                                                    {event && (
                                                        <>
                                                            {event.icon && event?.showIcon && (
                                                                <div
                                                                    className={`absolute top-1 flex items-center justify-center text-white text-xs font-bold`}>
                                                                    {event?.icon}
                                                                </div>
                                                            )}

                                                            {event?.tooltip.title === 'Microsoft' && (
                                                                <div
                                                                    className={`absolute top-1 hidden group-hover:flex items-center justify-center text-white text-xs font-bold `}>
                                                                    {event?.icon}
                                                                </div>
                                                            )}

                                                            {event?.showTooltip && (
                                                                <TooltipContent
                                                                    side="top"
                                                                    className="p-0 border-0 shadow-none bg-transparent "
                                                                    hideArrow>
                                                                    <div className="bg-white rounded-2xl p-2 shadow-lg border min-w-[170px] ">
                                                                        <div className="flex items-center gap-2 mb-2">
                                                                            <div
                                                                                className={`w-4 h-4 rounded ${event?.color} flex items-center justify-center text-white text-xs`}>
                                                                                {event.icon}
                                                                            </div>
                                                                            <span className="font-semibold text-gray-900">
                                                                                {event.tooltip.title}
                                                                            </span>
                                                                            <span className="font-semibold text-gray-900 ml-auto">
                                                                                {event.tooltip.amount}
                                                                            </span>
                                                                        </div>
                                                                        <div className="flex justify-between">
                                                                            <div className="text-[8px] text-gray-600 mb-1">
                                                                                {event.tooltip.description}
                                                                            </div>
                                                                            <div className="text-[8px] text-gray-600 mb-1">
                                                                                {event.tooltip.nextAction}
                                                                            </div>
                                                                        </div>
                                                                        <div className="flex justify-between">
                                                                            <div className="text-gray-600 text-[9px]">
                                                                                <p>Total Since</p>
                                                                                <p>{event.tooltip.since}</p>
                                                                            </div>
                                                                            <p className="text-sm font-semibold text-gray-900">
                                                                                {event.tooltip.total}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </TooltipContent>
                                                            )}
                                                        </>
                                                    )}
                                                    <span className="text-sm absolute bottom-0">{day}</span>
                                                </>
                                            )}
                                        </div>
                                    </TooltipTrigger>
                                </Tooltip>
                            )
                        })}
                    </div>
                </div>
            </div>
        </TooltipProvider>
    )
}
