import { useQuery } from '@tanstack/react-query'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from './ui/dialog'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { useAuthStore } from '@/store/auth.store'
import { useCallback, useEffect, useState } from 'react'
import { Input } from './ui/input'
import { Loader, Search } from 'lucide-react'
import { ScrollArea } from './ui/scroll-area'
import { Button } from './ui/button'
import { debounce } from 'lodash'
import { AssigneeAvatar } from './assignee-avatar-with-fallback'

interface AddUsersModalProps {
    project_id?: number
    children: React.ReactNode
    onSubmit?: (users: number[], closeModal: () => void) => void
    alreadyAssignedUsers?: number[]
    dialogueTitle?: string
    dialogueDescription?: string
    buttonText?: string
}

const AddUsersModal = ({
    project_id,
    children,
    onSubmit,
    alreadyAssignedUsers,
    dialogueTitle = 'Add',
    dialogueDescription = '',
    buttonText = 'Add Assignees',
}: AddUsersModalProps) => {
    const { currentWorkspace } = useAuthStore()
    const [searchQuery, setSearchQuery] = useState('')
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [selectedUsers, setSelectedUsers] = useState<number[]>(alreadyAssignedUsers || [])

    useEffect(() => {
        if (alreadyAssignedUsers && alreadyAssignedUsers?.length > 0) setSelectedUsers(alreadyAssignedUsers)
    }, [alreadyAssignedUsers])

    const { data: users, isLoading } = useQuery({
        queryKey: ['users', debouncedSearchQuery],
        queryFn: async () => {
            const response = await api.get(endpoints.meta.getAssignees, {
                params: {
                    project_id: project_id,
                    workspace_id: currentWorkspace?.id,
                    searchQuery: debouncedSearchQuery,
                },
            })
            return response.data.data
        },
        enabled: !!currentWorkspace?.id,
    })

    // Create debounced search function with proper cleanup
    const debouncedSetQuery = useCallback(
        (value: string) => {
            const debouncedFn = debounce((searchValue: string) => {
                setDebouncedSearchQuery(searchValue)
            }, 300)

            debouncedFn(value)

            // Return cleanup function
            return () => {
                debouncedFn.cancel()
            }
        },
        [setDebouncedSearchQuery],
    )

    const handleSearchWithDebounce = (value: string) => {
        setSearchQuery(value)
        debouncedSetQuery(value)
    }

    const handleUserSelect = (userId: number) => {
        if (selectedUsers.includes(userId)) {
            setSelectedUsers(selectedUsers.filter((id) => id !== userId))
        } else {
            setSelectedUsers([...selectedUsers, userId])
        }
    }

    const closeModal = (isCancel: boolean = false) => {
        setIsModalOpen(false)
        setSearchQuery('')
        setDebouncedSearchQuery('')
        if (isCancel) setSelectedUsers(alreadyAssignedUsers || [])
    }

    const onOpenChange = (open: boolean) => {
        setIsModalOpen(open)
        if (!open) {
            setSelectedUsers(alreadyAssignedUsers || [])
            setSearchQuery('')
            setDebouncedSearchQuery('')
        }
    }

    if (isLoading)
        return (
            <div className="flex h-full items-center justify-center">
                <Loader className="h-6 w-6 animate-spin text-gray-400" />
            </div>
        )

    return (
        <Dialog open={isModalOpen} onOpenChange={onOpenChange}>
            <DialogTrigger asChild className="cursor-pointer">
                {children}
            </DialogTrigger>
            <DialogContent className="w-[441px]">
                <DialogHeader>
                    <DialogTitle>{dialogueTitle}</DialogTitle>
                    <DialogDescription>{dialogueDescription}</DialogDescription>
                </DialogHeader>
                <div className="relative">
                    <Input
                        type="text"
                        value={searchQuery}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSearchWithDebounce(e.target.value)}
                        placeholder="Search users"
                        className="h-[38px] pl-7"
                    />
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
                <ScrollArea className="h-40 p-2">
                    {users?.map((user: { id: number; first_name: string; last_name: string; img_url: string }) => (
                        <label key={user.id} htmlFor={`user-${user.id}`} className="flex items-center gap-2 cursor-pointer mb-2">
                            <Input
                                id={`user-${user.id}`}
                                className="h-4 w-4"
                                type="checkbox"
                                name="users"
                                value={user.id}
                                checked={selectedUsers?.includes(user.id)}
                                onChange={() => handleUserSelect(user.id)}
                            />
                            <AssigneeAvatar
                                assignee={user.first_name + ' ' + user.last_name}
                                imageUrl={user.img_url}
                                className="h-[32px] w-[32px]"
                                showTooltip={false}
                            />
                            <span className="text-sm font-medium text-[#414651]">
                                {user.first_name} {user.last_name}
                            </span>
                        </label>
                    ))}
                </ScrollArea>
                <div className="w-full flex gap-2 h-[45px]">
                    <Button
                        variant="ghost"
                        className="text-[#18181B] w-1/2 h-full bg-[#F4F4F5] shadow-none text-sm font-medium"
                        onClick={() => closeModal(true)}>
                        Cancel
                    </Button>

                    <Button
                        className="w-1/2 h-full shadow-none text-sm font-medium"
                        onClick={() => onSubmit?.(selectedUsers, closeModal)}>
                        {buttonText}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default AddUsersModal
