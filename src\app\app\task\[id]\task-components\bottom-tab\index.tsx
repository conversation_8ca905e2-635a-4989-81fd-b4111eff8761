'use client'

import React from 'react'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import CommentsTab from './comments'
import { useTabStore } from '@/store/tabs.store'
import ActivityLogTab from './activity-log'
import { Logs, MessageCircleMore } from 'lucide-react'

interface TabItem {
    id: string
    label: string
    icon?: React.ReactNode
    content: React.ReactNode
    count?: number
}

export default function TaskBottomTabs() {
    const { taskActiveTab, setTaskActiveTab } = useTabStore()

    // Create an array of tab items with optional count badges
    const tabItems: TabItem[] = [
        {
            id: 'comments',
            label: 'Discussion',
            content: <CommentsTab />,
            icon: <MessageCircleMore className="h-4 w-4" />,
        },
        {
            id: 'activityLog',
            label: 'Activity Log',
            content: <ActivityLogTab />,
            icon: <Logs className="h-4 w-4" />,
        },
    ]

    return (
        <div className="flex rounded-lg bg-transparent pb-4 items-center">
            <Tabs value={taskActiveTab} onValueChange={setTaskActiveTab} className="w-full">
                <div className="flex flex-row justify-between items-center w-full">
                    <TabsList className="grid grid-cols-2 h-full ">
                        {tabItems.map((tab) => (
                            <TabsTrigger
                                key={tab.id}
                                value={tab.id}
                                className="flex items-center gap-2 py-2 data-[state=active]:shadow-none  data-[state=inactive]:text-[#575757]">
                                {tab?.icon}
                                <span>{tab.label}</span>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>
                {tabItems.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="bg-[#F4F4F570] rounded-[8px] p-4">
                        {tab.content}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    )
}
