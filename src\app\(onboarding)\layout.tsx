import Image from 'next/image'

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <div className="w-full h-screen flex justify-center items-center ">
            <Image
                src="/assets/img/auth-background-image.png"
                fill
                alt="Auth background image"
                className="absolute object-cover h-full w-full -z-10"
            />
            <div className="w-full ">
                {/* <Link
                    href="/"
                    className="flex items-center gap-2 hover:opacity-80 transition-opacity absolute top-20 left-10 md:top-8 sm:top-0 md:left-61 sm:left-20  z-10">
                    <Image src="/assets/img/raydian-logo.png" alt="Logo" width={28} height={28} className="h-auto w-auto" />
                    <span className="font-semibold text-lg">Raydian</span>
                </Link> */}

                <div className="w-full z-1">{children}</div>
            </div>
        </div>
    )
}
