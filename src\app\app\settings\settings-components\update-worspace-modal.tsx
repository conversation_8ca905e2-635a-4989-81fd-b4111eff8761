'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Loader, Upload } from 'lucide-react'
import Image from 'next/image'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

import { Form, FormField } from '@/components/ui/form'
import { FormInputField } from '@/components/form-fields'
import { api } from '@/config/axios-config'
import endpoints from '@/services/api-endpoints'
import { DialogDescription } from '@radix-ui/react-dialog'
import { uploadFile } from '@/lib/minio-client'
import { AxiosError } from 'axios'
import { axiosErrorToast } from '@/utils/axios-error-toast.utils'
import workIcon from '../../../../../public/assets/icons/workIcon.svg'
import { useAuthStore } from '@/store/auth.store'

type CreateWorkspaceModalProps = {
    open: boolean
    onOpenChange: (open: boolean) => void
    workspaceData: { id: number; name: string; img_url: string }
    refetchWorkspaces: () => void
}

type FileUploadResult = {
    success: boolean
    fileUrl?: string | null
}

const formSchema = z.object({
    workspaceName: z.string().min(1, 'Workspace Name is required'),
})

export default function UpdateWorkspaceModal({
    open,
    onOpenChange,
    workspaceData,
    refetchWorkspaces,
}: CreateWorkspaceModalProps) {
    const [logoImage, setLogoImage] = useState<File | null>(null)
    const { fetchMyDetails } = useAuthStore()

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            workspaceName: workspaceData?.name,
        },
    })

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setLogoImage(e.target.files[0])
        }
    }

    const removeImage = () => setLogoImage(null)

    const mutation = useMutation({
        mutationFn: async (data: z.infer<typeof formSchema>) => {
            let result: FileUploadResult = { success: false, fileUrl: '' } // Initialize result
            let body: { name: string; plan: number; img_url?: string } = {
                name: data.workspaceName,
                plan: 0,
            }
            if (logoImage) {
                const formData = new FormData()
                formData.append('file', logoImage)
                result = await uploadFile(formData)
            }

            if (logoImage && !result.success) {
                throw new Error('Upload failed')
            }
            if (result.fileUrl) {
                body = { ...body, img_url: result.fileUrl }
            }

            const response = await api.put(endpoints.workspace.updateWorkspace.replace(':id', workspaceData.id.toString()), body)
            return response.data
        },

        onSuccess: async () => {
            toast.success('Workspace updated successfully!')
            onOpenChange(false)
            removeImage()
            fetchMyDetails()
            refetchWorkspaces()
        },
        onError: (error: AxiosError) => {
            axiosErrorToast(error, 'Failed to update workspace. Please try again.')
        },
    })

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        const toastId = toast.loading('Updating workspace...')
        mutation.mutate(values, {
            onSettled: () => toast.dismiss(toastId),
        })
    }

    return (
        <>
            <Dialog
                modal
                open={open}
                onOpenChange={() => {
                    onOpenChange(false)
                    form.reset()
                    removeImage()
                }}>
                <DialogContent className="w-[475px]">
                    <DialogHeader>
                        <div className="border w-[36px] p-2 rounded-[10px]">
                            <Image src={workIcon} alt="Default Logo" width={26} height={26} className="mx-auto" />
                        </div>
                        <DialogTitle>Update Workspace</DialogTitle>
                        <DialogDescription className="text-[#71717A] text-xs">
                            Your ideas deserve a home. Start building together.
                        </DialogDescription>
                    </DialogHeader>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* Logo upload */}
                            <div className="w-full">
                                <div className="flex items-center space-x-3">
                                    <div className="relative w-12 h-12 rounded-full border overflow-hidden bg-[#08B38B0D] flex items-center justify-center">
                                        {logoImage && (
                                            <Image
                                                src={URL.createObjectURL(logoImage)}
                                                alt="Workspace Logo"
                                                width={48}
                                                height={48}
                                                className="object-cover"
                                            />
                                        )}
                                        {!logoImage && workspaceData?.img_url && (
                                            <Image
                                                src={workspaceData?.img_url}
                                                alt="Workspace Logo"
                                                width={48}
                                                height={48}
                                                className="object-cover"
                                            />
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-2">Workspace Logo</label>

                                        <div className="flex items-center space-x-2">
                                            <Button variant="outline" size="sm" className="text-xs" asChild>
                                                <label>
                                                    Upload Image
                                                    <input
                                                        type="file"
                                                        className="hidden"
                                                        accept="image/*"
                                                        onChange={handleImageUpload} // Handle file selection
                                                    />
                                                    <Upload className="ml-1 h-3 w-3" />
                                                </label>
                                            </Button>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                className="text-xs text-[#09090B]"
                                                onClick={removeImage}
                                                disabled={!logoImage}>
                                                Remove
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <FormField
                                control={form.control}
                                name="workspaceName"
                                render={({ field }) => (
                                    <FormInputField
                                        field={field}
                                        label="Workspace Name *"
                                        placeholder="Ex: Acme Inc"
                                        labelClassName="text-black"
                                    />
                                )}
                            />

                            <Button type="submit" className="w-full" disabled={mutation.isPending}>
                                {mutation.isPending ? <Loader className="animate-spin h-4 w-4 mr-2" /> : null}
                                Continue
                            </Button>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>
        </>
    )
}
